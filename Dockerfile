# ────────────────────────────────────────────────────────────────────────
#  Base image
# ────────────────────────────────────────────────────────────────────────
FROM python:3.11-slim

# ────────────────────────────────────────────────────────────────────────
#  System packages (ffmpeg for Whisper + build deps for psycopg2 if needed)
# ────────────────────────────────────────────────────────────────────────
RUN apt-get update && apt-get install -y --no-install-recommends \
        ffmpeg \
        gcc \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# ────────────────────────────────────────────────────────────────────────
#  Project setup
# ────────────────────────────────────────────────────────────────────────
WORKDIR /app

# Copy poetry manifest first to leverage Docker layer cache
COPY pyproject.toml ./

# Install Poetry & plugin, then export lockfile → requirements.txt
RUN pip install --upgrade pip poetry \
    && pip install poetry-plugin-export \
    && poetry export -f requirements.txt --output requirements.txt --without-hashes \
    && pip install -r requirements.txt

# Now copy the source code (done after deps to maximize caching)
COPY api ./api

ENV PYTHONPATH=/app

# ────────────────────────────────────────────────────────────────────────
#  Entrypoint: migrate, then run API
# ────────────────────────────────────────────────────────────────────────
CMD ["bash", "-c", "alembic upgrade head && uvicorn api.main:app --host 0.0.0.0 --port 8000"]
