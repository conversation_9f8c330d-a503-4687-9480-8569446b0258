# ────────────────────────────────────────────────────────────────────────
#  Cloud Build Configuration for Cloud Run Deployment
# ────────────────────────────────────────────────────────────────────────

steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/mnemos-api:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/mnemos-api:latest',
      '-f', 'deployment/cloud-run/Dockerfile',
      '.'
    ]

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/mnemos-api:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/mnemos-api:latest']

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', 'mnemos-api',
      '--image', 'gcr.io/$PROJECT_ID/mnemos-api:$COMMIT_SHA',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '1Gi',
      '--cpu', '1',
      '--max-instances', '10',
      '--concurrency', '80',
      '--timeout', '300',
      '--set-env-vars', 'PORT=8080',
      '--set-env-vars', 'PYTHONPATH=/app'
    ]

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/mnemos-api:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/mnemos-api:latest'

# Build configuration
options:
  machineType: 'E2_HIGHCPU_8'
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'
