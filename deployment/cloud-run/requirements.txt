# ────────────────────────────────────────────────────────────────────────
#  Cloud Run Requirements - Minimal API Dependencies Only
# ────────────────────────────────────────────────────────────────────────

# Core FastAPI and web framework
fastapi[standard]==0.111.*
uvicorn[standard]==0.30.*

# Database and ORM
sqlalchemy==2.0.*
psycopg2-binary==2.9.*
alembic==1.16.*

# Task queue client (for submitting tasks, not processing)
celery[redis]==5.3.*
redis==5.0.*

# Configuration and environment
python-dotenv==1.0.*
pydantic-settings==2.2.*

# LangChain core (lightweight, for data models)
langchain-core

# HTTP client for external API calls
httpx==0.27.*

# Document processing (lightweight parsers)
pypdf==4.3.*
python-pptx==0.6.*

# Cloud storage clients
boto3==1.34.*  # AWS S3
google-cloud-storage==2.17.*  # GCS

# Logging and monitoring
structlog==24.1.*

# Note: Heavy ML dependencies are excluded:
# - No faster-whisper (audio processing)
# - No sentence-transformers (embedding models)
# - No torch/transformers (ML frameworks)
# - No qdrant-client with heavy dependencies
# These run on Compute Engine workers instead
