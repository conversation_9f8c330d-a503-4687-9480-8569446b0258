# ────────────────────────────────────────────────────────────────────────
#  Cloud Run Environment Variables Template
# ────────────────────────────────────────────────────────────────────────

# FastAPI Configuration
API_KEY=your-api-key-here

# Database (Supabase/PostgreSQL)
DATABASE_URL=postgresql://user:password@host:port/database

# External Services (running on Compute Engine VM)
# Replace VM_EXTERNAL_IP with your Compute Engine VM's external IP
QDRANT_HOST=VM_EXTERNAL_IP
QDRANT_PORT=6333
QDRANT_COLLECTION=video_chunks

# Celery (Redis on Compute Engine VM)
CELERY_BROKER_URL=redis://VM_EXTERNAL_IP:6379/0
CELERY_RESULT_BACKEND=redis://VM_EXTERNAL_IP:6379/1

# LLM and Embedding Providers
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini
EMBED_PROVIDER=openai
EMBED_MODEL=text-embedding-3-small

# API Keys
OPENAI_API_KEY=your-openai-api-key
HF_API_TOKEN=your-huggingface-token
TOGETHER_API_KEY=your-together-api-key

# Cloud Storage Configuration
CLOUD_STORAGE_PROVIDER=gcs  # or s3

# Google Cloud Storage (recommended for GCP deployment)
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GCS_BUCKET=your-bucket-name

# AWS S3 (alternative)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# Cloud Run Specific
PORT=8080  # Set by Cloud Run automatically

# Note: ML model configurations are not needed here
# as they run on Compute Engine workers
