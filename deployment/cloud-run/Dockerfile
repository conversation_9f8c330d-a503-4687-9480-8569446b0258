# ────────────────────────────────────────────────────────────────────────
#  <PERSON> Run Optimized Dockerfile - Stateless FastAPI API
# ────────────────────────────────────────────────────────────────────────
FROM python:3.11-slim

# ────────────────────────────────────────────────────────────────────────
#  System packages (minimal for API only)
# ────────────────────────────────────────────────────────────────────────
RUN apt-get update && apt-get install -y --no-install-recommends \
        gcc \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# ────────────────────────────────────────────────────────────────────────
#  Project setup
# ────────────────────────────────────────────────────────────────────────
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy only the API source code (no ML models, no heavy dependencies)
COPY api ./api
COPY alembic.ini ./

# Set Python path
ENV PYTHONPATH=/app

# ────────────────────────────────────────────────────────────────────────
#  Cloud Run specific optimizations
# ────────────────────────────────────────────────────────────────────────
# Use PORT environment variable provided by Cloud Run
ENV PORT=8080

# Run database migrations and start the API
CMD alembic upgrade head && uvicorn api.main:app --host 0.0.0.0 --port $PORT --workers 1
