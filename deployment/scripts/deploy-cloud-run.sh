#!/bin/bash
# ────────────────────────────────────────────────────────────────────────
#  Cloud Run Deployment Script
# ────────────────────────────────────────────────────────────────────────

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
REGION=${2:-"us-central1"}
SERVICE_NAME="mnemos-api"
VM_EXTERNAL_IP=${3:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Validate inputs
if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    error "Please provide a valid GCP project ID as the first argument"
fi

if [ -z "$VM_EXTERNAL_IP" ]; then
    warn "VM external IP not provided. You'll need to update environment variables manually."
fi

log "Starting Cloud Run deployment for project: $PROJECT_ID"

# ────────────────────────────────────────────────────────────────────────
#  Prerequisites Check
# ────────────────────────────────────────────────────────────────────────
log "Checking prerequisites..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    error "gcloud CLI is not installed. Please install it first."
fi

# Check if logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    error "Not logged in to gcloud. Run 'gcloud auth login' first."
fi

# Set project
gcloud config set project $PROJECT_ID

# Enable required APIs
log "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# ────────────────────────────────────────────────────────────────────────
#  Build and Deploy
# ────────────────────────────────────────────────────────────────────────
log "Building and deploying to Cloud Run..."

# Navigate to project root
cd "$(dirname "$0")/../.."

# Create requirements.txt from Cloud Run template
cp deployment/cloud-run/requirements.txt .

# Build and deploy using Cloud Build
gcloud builds submit \
    --config deployment/cloud-run/cloudbuild.yaml \
    --substitutions _SERVICE_NAME=$SERVICE_NAME,_REGION=$REGION

# ────────────────────────────────────────────────────────────────────────
#  Environment Variables Setup
# ────────────────────────────────────────────────────────────────────────
log "Setting up environment variables..."

# Create environment variables file for Cloud Run
ENV_VARS=""

if [ -n "$VM_EXTERNAL_IP" ]; then
    ENV_VARS="$ENV_VARS,QDRANT_HOST=$VM_EXTERNAL_IP"
    ENV_VARS="$ENV_VARS,CELERY_BROKER_URL=redis://$VM_EXTERNAL_IP:6379/0"
    ENV_VARS="$ENV_VARS,CELERY_RESULT_BACKEND=redis://$VM_EXTERNAL_IP:6379/1"
fi

# Update service with environment variables
if [ -n "$ENV_VARS" ]; then
    log "Updating service with VM connection details..."
    gcloud run services update $SERVICE_NAME \
        --region=$REGION \
        --set-env-vars="${ENV_VARS:1}"  # Remove leading comma
fi

# ────────────────────────────────────────────────────────────────────────
#  Get Service URL
# ────────────────────────────────────────────────────────────────────────
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

log "Deployment completed successfully!"
log "Service URL: $SERVICE_URL"
log ""
log "Next steps:"
log "1. Update your .env file with the following:"
log "   - Set QDRANT_HOST=$VM_EXTERNAL_IP"
log "   - Set CELERY_BROKER_URL=redis://$VM_EXTERNAL_IP:6379/0"
log "   - Set CELERY_RESULT_BACKEND=redis://$VM_EXTERNAL_IP:6379/1"
log "2. Test the deployment: curl $SERVICE_URL/health"
log "3. Monitor logs: gcloud run services logs tail $SERVICE_NAME --region=$REGION"

# Clean up
rm -f requirements.txt
