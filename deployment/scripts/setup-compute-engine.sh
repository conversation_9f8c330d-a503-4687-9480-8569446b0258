#!/bin/bash
# ────────────────────────────────────────────────────────────────────────
#  Compute Engine VM Setup Sc<PERSON>t
# ────────────────────────────────────────────────────────────────────────

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
ZONE=${2:-"us-central1-a"}
INSTANCE_NAME="mnemos-backend"
MACHINE_TYPE="e2-standard-4"  # 4 vCPUs, 16GB RAM
DISK_SIZE="50GB"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Validate inputs
if [ -z "$PROJECT_ID" ] || [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    error "Please provide a valid GCP project ID as the first argument"
fi

log "Setting up Compute Engine VM for project: $PROJECT_ID"

# ────────────────────────────────────────────────────────────────────────
#  Prerequisites Check
# ────────────────────────────────────────────────────────────────────────
log "Checking prerequisites..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    error "gcloud CLI is not installed. Please install it first."
fi

# Set project
gcloud config set project $PROJECT_ID

# Enable required APIs
log "Enabling required APIs..."
gcloud services enable compute.googleapis.com

# ────────────────────────────────────────────────────────────────────────
#  Create VM Instance
# ────────────────────────────────────────────────────────────────────────
log "Creating Compute Engine instance..."

# Check if instance already exists
if gcloud compute instances describe $INSTANCE_NAME --zone=$ZONE &>/dev/null; then
    warn "Instance $INSTANCE_NAME already exists. Skipping creation."
else
    # Create the instance
    gcloud compute instances create $INSTANCE_NAME \
        --zone=$ZONE \
        --machine-type=$MACHINE_TYPE \
        --network-interface=network-tier=PREMIUM,subnet=default \
        --maintenance-policy=MIGRATE \
        --provisioning-model=STANDARD \
        --service-account=$(gcloud iam service-accounts list --format="value(email)" --filter="displayName:Compute Engine default service account") \
        --scopes=https://www.googleapis.com/auth/cloud-platform \
        --tags=mnemos-backend,http-server,https-server \
        --create-disk=auto-delete=yes,boot=yes,device-name=$INSTANCE_NAME,image=projects/debian-cloud/global/images/family/debian-12,mode=rw,size=$DISK_SIZE,type=projects/$PROJECT_ID/zones/$ZONE/diskTypes/pd-standard \
        --no-shielded-secure-boot \
        --shielded-vtpm \
        --shielded-integrity-monitoring \
        --labels=environment=production,service=mnemos-backend \
        --reservation-affinity=any
fi

# ────────────────────────────────────────────────────────────────────────
#  Firewall Rules
# ────────────────────────────────────────────────────────────────────────
log "Setting up firewall rules..."

# Qdrant API
if ! gcloud compute firewall-rules describe allow-qdrant &>/dev/null; then
    gcloud compute firewall-rules create allow-qdrant \
        --allow tcp:6333 \
        --source-ranges 0.0.0.0/0 \
        --target-tags mnemos-backend \
        --description "Allow Qdrant API access"
fi

# Redis
if ! gcloud compute firewall-rules describe allow-redis &>/dev/null; then
    gcloud compute firewall-rules create allow-redis \
        --allow tcp:6379 \
        --source-ranges 0.0.0.0/0 \
        --target-tags mnemos-backend \
        --description "Allow Redis access"
fi

# Flower monitoring (optional)
if ! gcloud compute firewall-rules describe allow-flower &>/dev/null; then
    gcloud compute firewall-rules create allow-flower \
        --allow tcp:5555 \
        --source-ranges 0.0.0.0/0 \
        --target-tags mnemos-backend \
        --description "Allow Flower monitoring access"
fi

# ────────────────────────────────────────────────────────────────────────
#  Deploy Application
# ────────────────────────────────────────────────────────────────────────
log "Deploying application to VM..."

# Get the startup script
STARTUP_SCRIPT="deployment/compute-engine/startup-script.sh"

# Copy files to VM
gcloud compute scp --recurse deployment/ $INSTANCE_NAME:~/deployment/ --zone=$ZONE
gcloud compute scp --recurse api/ $INSTANCE_NAME:~/api/ --zone=$ZONE
gcloud compute scp pyproject.toml poetry.lock alembic.ini $INSTANCE_NAME:~/ --zone=$ZONE

# Run startup script
gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command="
    chmod +x ~/deployment/compute-engine/startup-script.sh
    sudo ~/deployment/compute-engine/startup-script.sh
"

# ────────────────────────────────────────────────────────────────────────
#  Get Instance Information
# ────────────────────────────────────────────────────────────────────────
EXTERNAL_IP=$(gcloud compute instances describe $INSTANCE_NAME --zone=$ZONE --format="value(networkInterfaces[0].accessConfigs[0].natIP)")
INTERNAL_IP=$(gcloud compute instances describe $INSTANCE_NAME --zone=$ZONE --format="value(networkInterfaces[0].networkIP)")

log "VM setup completed successfully!"
log ""
log "Instance Details:"
log "  Name: $INSTANCE_NAME"
log "  Zone: $ZONE"
log "  External IP: $EXTERNAL_IP"
log "  Internal IP: $INTERNAL_IP"
log ""
log "Services available at:"
log "  Qdrant: http://$EXTERNAL_IP:6333"
log "  Redis: $EXTERNAL_IP:6379"
log "  Flower: http://$EXTERNAL_IP:5555"
log ""
log "Next steps:"
log "1. SSH to VM: gcloud compute ssh $INSTANCE_NAME --zone=$ZONE"
log "2. Check logs: sudo docker-compose -f /opt/mnemos/deployment/compute-engine/docker-compose.yml logs"
log "3. Update Cloud Run environment with External IP: $EXTERNAL_IP"
log "4. Deploy Cloud Run: ./deployment/scripts/deploy-cloud-run.sh $PROJECT_ID us-central1 $EXTERNAL_IP"
