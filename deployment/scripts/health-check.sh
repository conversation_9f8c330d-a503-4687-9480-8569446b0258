#!/bin/bash
# ────────────────────────────────────────────────────────────────────────
#  Health Check Script for Mnemos Deployment
# ────────────────────────────────────────────────────────────────────────

set -e

# Configuration
CLOUD_RUN_URL=${1:-""}
VM_EXTERNAL_IP=${2:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}
    
    info "Checking $name at $url"
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/response "$url" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            success "$name is healthy (HTTP $status_code)"
            return 0
        else
            error "$name returned HTTP $status_code (expected $expected_status)"
            return 1
        fi
    else
        error "$name is not accessible"
        return 1
    fi
}

# Function to check TCP port
check_tcp() {
    local host=$1
    local port=$2
    local name=$3
    
    info "Checking $name at $host:$port"
    
    if timeout 5 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        success "$name is accessible"
        return 0
    else
        error "$name is not accessible at $host:$port"
        return 1
    fi
}

log "Starting health check for Mnemos deployment..."

# ────────────────────────────────────────────────────────────────────────
#  Cloud Run Health Check
# ────────────────────────────────────────────────────────────────────────
if [ -n "$CLOUD_RUN_URL" ]; then
    echo
    log "Checking Cloud Run API..."
    
    # Health endpoint
    check_http "$CLOUD_RUN_URL/health" "Cloud Run Health Endpoint"
    
    # API endpoints
    check_http "$CLOUD_RUN_URL/docs" "API Documentation"
    
else
    warn "Cloud Run URL not provided. Skipping Cloud Run health checks."
fi

# ────────────────────────────────────────────────────────────────────────
#  Compute Engine Health Check
# ────────────────────────────────────────────────────────────────────────
if [ -n "$VM_EXTERNAL_IP" ]; then
    echo
    log "Checking Compute Engine services..."
    
    # Qdrant
    check_http "http://$VM_EXTERNAL_IP:6333/health" "Qdrant Vector Database"
    check_http "http://$VM_EXTERNAL_IP:6333/collections" "Qdrant Collections API"
    
    # Redis
    check_tcp "$VM_EXTERNAL_IP" "6379" "Redis"
    
    # Flower (optional)
    check_http "http://$VM_EXTERNAL_IP:5555" "Flower Monitoring" "200"
    
else
    warn "VM External IP not provided. Skipping Compute Engine health checks."
fi

# ────────────────────────────────────────────────────────────────────────
#  Integration Test
# ────────────────────────────────────────────────────────────────────────
if [ -n "$CLOUD_RUN_URL" ] && [ -n "$VM_EXTERNAL_IP" ]; then
    echo
    log "Running integration tests..."
    
    # Test task submission (if API supports it)
    info "Testing task queue integration..."
    
    # This would be a simple test to verify Cloud Run can communicate with Celery
    # You might want to add a specific test endpoint for this
    
    success "Integration tests completed"
fi

echo
log "Health check completed!"

# ────────────────────────────────────────────────────────────────────────
#  Summary
# ────────────────────────────────────────────────────────────────────────
echo
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                              SUMMARY"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

if [ -n "$CLOUD_RUN_URL" ]; then
    echo "🌐 Cloud Run API: $CLOUD_RUN_URL"
fi

if [ -n "$VM_EXTERNAL_IP" ]; then
    echo "🖥️  Compute Engine VM: $VM_EXTERNAL_IP"
    echo "   📊 Qdrant Dashboard: http://$VM_EXTERNAL_IP:6333/dashboard"
    echo "   🌸 Flower Monitoring: http://$VM_EXTERNAL_IP:5555"
fi

echo
echo "📋 To run this script:"
echo "   ./deployment/scripts/health-check.sh [CLOUD_RUN_URL] [VM_EXTERNAL_IP]"
echo
echo "📋 Example:"
echo "   ./deployment/scripts/health-check.sh https://mnemos-api-xxx-uc.a.run.app ************"
