# Deployment Architecture

This directory contains deployment configurations for a cloud-native architecture that separates concerns between stateless API services and stateful backend services.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                        CLOUD DEPLOYMENT                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐              ┌─────────────────────────┐   │
│  │   CLOUD RUN     │              │   COMPUTE ENGINE VM     │   │
│  │                 │              │                         │   │
│  │  ┌───────────┐  │              │  ┌─────────────────┐    │   │
│  │  │ FastAPI   │  │◄─────────────┤  │ Qdrant Vector   │    │   │
│  │  │ API       │  │              │  │ Database        │    │   │
│  │  └───────────┘  │              │  └─────────────────┘    │   │
│  │                 │              │                         │   │
│  │  • Stateless    │              │  ┌─────────────────┐    │   │
│  │  • Auto-scaling │              │  │ Redis Cache &   │    │   │
│  │  • HTTP only    │              │  │ Celery Broker   │    │   │
│  │  • No ML models │              │  └─────────────────┘    │   │
│  └─────────────────┘              │                         │   │
│                                   │  ┌─────────────────┐    │   │
│                                   │  │ Celery Workers  │    │   │
│                                   │  │ + ML Models     │    │   │
│                                   │  │ (Whisper, etc.) │    │   │
│                                   │  └─────────────────┘    │   │
│                                   └─────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Component Separation

### Cloud Run (Stateless API)
- **Purpose**: Handle HTTP requests, coordinate tasks, return results
- **Components**:
  - FastAPI application (`api/main.py`)
  - API routers (`api/routers/`)
  - Database models and connections (`api/models.py`, `api/db.py`)
  - Task coordination (submit to Celery, check status)
- **Characteristics**:
  - Stateless and horizontally scalable
  - No file storage or ML model inference
  - Connects to external services (Qdrant, Redis, PostgreSQL)
  - Fast startup time

### Compute Engine (Stateful Backend)
- **Purpose**: Heavy computation, data storage, background processing
- **Components**:
  - Qdrant vector database
  - Redis (Celery broker + cache)
  - Celery workers (`api/tasks.py`)
  - ML models (Whisper, embeddings, LLMs)
  - File storage and processing
- **Characteristics**:
  - Stateful with persistent storage
  - Resource-intensive operations
  - Long-running processes
  - Optimized for compute workloads

## Directory Structure

```
deployment/
├── README.md                    # This file
├── cloud-run/                  # Cloud Run deployment files
│   ├── Dockerfile              # Optimized for stateless API
│   ├── requirements.txt        # Minimal dependencies
│   ├── .env.template          # Environment variables for Cloud Run
│   └── cloudbuild.yaml        # Cloud Build configuration
├── compute-engine/             # Compute Engine deployment files
│   ├── docker-compose.yml     # All backend services
│   ├── Dockerfile.worker      # Celery worker with ML dependencies
│   ├── startup-script.sh      # VM initialization script
│   ├── .env.template         # Environment variables for VM
│   └── configs/              # Service configurations
│       ├── qdrant.yaml
│       └── redis.conf
└── scripts/                   # Deployment automation scripts
    ├── deploy-cloud-run.sh
    ├── setup-compute-engine.sh
    └── health-check.sh
```

## Key Benefits

1. **Cost Optimization**: Cloud Run scales to zero, Compute Engine runs only what's needed
2. **Performance**: API responses are fast, heavy processing happens asynchronously
3. **Scalability**: API can scale independently from backend services
4. **Reliability**: Separation of concerns reduces failure impact
5. **Maintenance**: Clear boundaries make updates and debugging easier

## Deployment Flow

1. **Compute Engine VM**: Deploy first with all backend services
2. **Cloud Run API**: Deploy API that connects to VM services
3. **Configuration**: Update environment variables to connect services
4. **Testing**: Verify end-to-end functionality

## Quick Start Deployment

### 1. Deploy Compute Engine Backend (First)
```bash
# Make scripts executable
chmod +x deployment/scripts/*.sh

# Set up the backend VM with all services
./deployment/scripts/setup-compute-engine.sh YOUR_PROJECT_ID us-central1-a

# Note the External IP from the output
```

### 2. Deploy Cloud Run API (Second)
```bash
# Deploy the API with VM connection
./deployment/scripts/deploy-cloud-run.sh YOUR_PROJECT_ID us-central1 VM_EXTERNAL_IP
```

### 3. Verify Deployment
```bash
# Run health checks
./deployment/scripts/health-check.sh CLOUD_RUN_URL VM_EXTERNAL_IP
```

## Environment Variables

### Shared Configuration
- Database connections (PostgreSQL/Supabase)
- API keys (OpenAI, HuggingFace, etc.)
- Cloud storage settings

### Cloud Run Specific
- External service endpoints (Qdrant, Redis on VM)
- Minimal resource allocation

### Compute Engine Specific
- Internal service networking
- ML model configurations
- Storage mount points

## Manual Configuration Steps

### 1. Update Environment Files
After deployment, update the `.env` files in both locations:

**Cloud Run** (`deployment/cloud-run/.env.template`):
- Set `QDRANT_HOST` to VM external IP
- Set `CELERY_BROKER_URL` to VM Redis endpoint
- Add your API keys

**Compute Engine** (`deployment/compute-engine/.env.template`):
- Configure ML model settings
- Set cloud storage credentials
- Add your API keys

### 2. Security Considerations
- Use Google Cloud Secret Manager for sensitive data
- Restrict firewall rules to specific IP ranges if possible
- Enable VPC for internal communication
- Use IAM roles for service authentication

### 3. Monitoring and Logging
- Cloud Run: Built-in logging and monitoring
- Compute Engine: Use `docker-compose logs` for service logs
- Flower: Monitor Celery tasks at `http://VM_IP:5555`
- Qdrant: Dashboard at `http://VM_IP:6333/dashboard`
