# ────────────────────────────────────────────────────────────────────────
#  Compute Engine Worker Dockerfile - ML Processing & Background Tasks
# ────────────────────────────────────────────────────────────────────────
FROM python:3.11-slim

# ────────────────────────────────────────────────────────────────────────
#  System packages (full ML dependencies)
# ────────────────────────────────────────────────────────────────────────
RUN apt-get update && apt-get install -y --no-install-recommends \
        ffmpeg \
        gcc \
        g++ \
        libpq-dev \
        libsndfile1 \
        git \
        curl \
    && rm -rf /var/lib/apt/lists/*

# ────────────────────────────────────────────────────────────────────────
#  Project setup
# ────────────────────────────────────────────────────────────────────────
WORKDIR /app

# Copy poetry manifest first to leverage Docker layer cache
COPY pyproject.toml poetry.lock ./

# Install Poetry & dependencies (full ML stack)
RUN pip install --upgrade pip poetry \
    && pip install poetry-plugin-export \
    && poetry export -f requirements.txt --output requirements.txt --without-hashes \
    && pip install -r requirements.txt

# Copy the source code
COPY api ./api
COPY alembic.ini ./

# Set Python path
ENV PYTHONPATH=/app

# ────────────────────────────────────────────────────────────────────────
#  ML Model optimization
# ────────────────────────────────────────────────────────────────────────
# Pre-download common models to reduce startup time
RUN python -c "
import os
os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '1'
try:
    from sentence_transformers import SentenceTransformer
    # Pre-download embedding model
    SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
    print('Downloaded embedding model')
except Exception as e:
    print(f'Could not pre-download models: {e}')
"

# ────────────────────────────────────────────────────────────────────────
#  Health check
# ────────────────────────────────────────────────────────────────────────
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=3 \
    CMD celery -A api.celery_app inspect ping || exit 1

# Default command (can be overridden in docker-compose)
CMD ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "--concurrency=2"]
