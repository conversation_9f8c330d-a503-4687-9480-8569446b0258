#!/bin/bash
# ────────────────────────────────────────────────────────────────────────
#  Compute Engine VM Startup Script
# ────────────────────────────────────────────────────────────────────────

set -e

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a /var/log/mnemos-startup.log
}

log "Starting Mnemos backend services setup..."

# ────────────────────────────────────────────────────────────────────────
#  System Updates and Docker Installation
# ────────────────────────────────────────────────────────────────────────
log "Updating system packages..."
apt-get update
apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    git \
    unzip

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    log "Installing Docker..."
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io
    systemctl enable docker
    systemctl start docker
fi

# Install Docker Compose if not present
if ! command -v docker-compose &> /dev/null; then
    log "Installing Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# ────────────────────────────────────────────────────────────────────────
#  Application Setup
# ────────────────────────────────────────────────────────────────────────
APP_DIR="/opt/mnemos"
REPO_URL="https://github.com/your-username/mnemos-ai.git"  # Update this

# Create application directory
mkdir -p $APP_DIR
cd $APP_DIR

# Clone or update repository
if [ ! -d ".git" ]; then
    log "Cloning repository..."
    git clone $REPO_URL .
else
    log "Updating repository..."
    git pull origin main
fi

# Navigate to deployment directory
cd deployment/compute-engine

# Copy environment template if .env doesn't exist
if [ ! -f ".env" ]; then
    log "Creating environment file from template..."
    cp .env.template .env
    log "Please update .env file with your configuration!"
fi

# ────────────────────────────────────────────────────────────────────────
#  Service Startup
# ────────────────────────────────────────────────────────────────────────
log "Starting backend services..."

# Pull latest images
docker-compose pull

# Start services
docker-compose up -d

# Wait for services to be healthy
log "Waiting for services to be ready..."
sleep 30

# Check service health
docker-compose ps

log "Checking service health..."
for service in qdrant redis celery-worker; do
    if docker-compose ps $service | grep -q "Up"; then
        log "✓ $service is running"
    else
        log "✗ $service failed to start"
        docker-compose logs $service
    fi
done

# ────────────────────────────────────────────────────────────────────────
#  Firewall Configuration
# ────────────────────────────────────────────────────────────────────────
log "Configuring firewall rules..."

# Allow Qdrant (6333) and Redis (6379) from Cloud Run
ufw allow 6333/tcp comment "Qdrant API"
ufw allow 6379/tcp comment "Redis"
ufw allow 5555/tcp comment "Flower monitoring"

# Enable firewall if not already enabled
ufw --force enable

log "Backend services setup completed!"
log "Services available at:"
log "  - Qdrant: http://$(curl -s ifconfig.me):6333"
log "  - Redis: $(curl -s ifconfig.me):6379"
log "  - Flower: http://$(curl -s ifconfig.me):5555"

# ────────────────────────────────────────────────────────────────────────
#  Create systemd service for auto-restart
# ────────────────────────────────────────────────────────────────────────
cat > /etc/systemd/system/mnemos-backend.service << EOF
[Unit]
Description=Mnemos Backend Services
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR/deployment/compute-engine
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl enable mnemos-backend.service
log "Created systemd service for auto-restart on boot"
