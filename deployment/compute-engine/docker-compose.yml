# ────────────────────────────────────────────────────────────────────────
#  Compute Engine Docker Compose - Backend Services
# ────────────────────────────────────────────────────────────────────────
version: "3.9"

services:
  # ──────────────────────────────────────────────────────────────────────
  #  Qdrant Vector Database
  # ──────────────────────────────────────────────────────────────────────
  qdrant:
    image: qdrant/qdrant:v1.14.0
    container_name: qdrant
    restart: unless-stopped
    volumes:
      - qdrant_data:/qdrant/storage
      - ./configs/qdrant.yaml:/qdrant/config/production.yaml
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # gRPC API
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ──────────────────────────────────────────────────────────────────────
  #  Redis (Celery Broker + Cache)
  # ──────────────────────────────────────────────────────────────────────
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./configs/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ──────────────────────────────────────────────────────────────────────
  #  Celery Workers (ML Processing)
  # ──────────────────────────────────────────────────────────────────────
  celery-worker:
    build:
      context: ../../  # Build from project root
      dockerfile: deployment/compute-engine/Dockerfile.worker
    container_name: celery-worker
    restart: unless-stopped
    env_file: .env
    depends_on:
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    volumes:
      - uploads_data:/data
      - models_cache:/root/.cache/huggingface
      - ../../api:/app/api  # Mount API code for development
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
    command: celery -A api.celery_app worker --loglevel=info --concurrency=2
    healthcheck:
      test: ["CMD", "celery", "-A", "api.celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3

  # ──────────────────────────────────────────────────────────────────────
  #  Celery Flower (Optional - Monitoring)
  # ──────────────────────────────────────────────────────────────────────
  flower:
    build:
      context: ../../
      dockerfile: deployment/compute-engine/Dockerfile.worker
    container_name: flower
    restart: unless-stopped
    env_file: .env
    depends_on:
      - redis
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    command: celery -A api.celery_app flower --port=5555

# ──────────────────────────────────────────────────────────────────────
#  Persistent Volumes
# ──────────────────────────────────────────────────────────────────────
volumes:
  qdrant_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  models_cache:
    driver: local

# ──────────────────────────────────────────────────────────────────────
#  Networks
# ──────────────────────────────────────────────────────────────────────
networks:
  default:
    name: mnemos-backend
