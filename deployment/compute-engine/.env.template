# ────────────────────────────────────────────────────────────────────────
#  Compute Engine Environment Variables Template
# ────────────────────────────────────────────────────────────────────────

# Database (Supabase/PostgreSQL) - shared with Cloud Run
DATABASE_URL=postgresql://user:password@host:port/database

# ML Model Configuration
WHISPER_MODEL_SIZE=medium
WHISPER_DEVICE=auto  # cpu | cuda | auto

# Embedding Provider Configuration
EMBED_PROVIDER=hf-local  # Use local models for better performance
EMBED_MODEL=sentence-transformers/all-MiniLM-L6-v2

# LLM Provider (for MCQ generation, etc.)
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini

# API Keys
OPENAI_API_KEY=your-openai-api-key
HF_API_TOKEN=your-huggingface-token
TOGETHER_API_KEY=your-together-api-key

# Local Service Configuration
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION=video_chunks

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/1

# Cloud Storage Configuration
CLOUD_STORAGE_PROVIDER=gcs  # or s3

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GCS_BUCKET=your-bucket-name

# AWS S3 (alternative)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# Performance Tuning
CELERY_WORKER_CONCURRENCY=2
CELERY_WORKER_PREFETCH_MULTIPLIER=1

# Logging
LOG_LEVEL=INFO

# Security (if needed)
API_KEY=your-api-key-here
