[tool.poetry]
name = "video-rag"
version = "0.1.0"
description = "Universal Video Retrieval‑Augmented Generation backend"
authors = ["Kobina Acqua<PERSON> <<EMAIL>>"]
packages = [{ include = "api" }]

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
#   keep alias-compatible version
langchain-core = "*"
langchain-openai = "*"
langchain-huggingface = "*"
langchain-together = "*"
# langchain-ollama = "*"
langchain-qdrant = "*"
langchain-community = "*"
# sentence_transformers = "*"
python-pptx = "*" 
pypdf = "*"
fastapi = { version = "^0.111", extras = ["standard"] }
openai = "^1.25"
faster-whisper = "^1.0.0"
qdrant-client = "^1.7"
celery = {version = "^5.3", extras = ["redis"]} 
redis = "^5.0"
python-dotenv = "^1.0"
pydantic-settings = "^2.2"
huggingface_hub = { version = "^0.23.0", extras = ["hf_xet"] }
alembic = "^1.16.1"
psycopg2-binary = "^2.9.10"


[tool.poetry.group.dev.dependencies]
black = "^24.4"
isort = "^5.13"
