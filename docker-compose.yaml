version: "3.9"

services:
  qdrant:
    image: qdrant/qdrant:v1.14.0
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6333:6333"

  api:
    build: .
    env_file: .env
    volumes:
      - .:/app
      - uploads_data:/data
    ports:
      - "8000:8000"
    depends_on:
      - qdrant
    command: >
      bash -c "alembic upgrade head && uvicorn api.main:app --host 0.0.0.0 --port 8000"

  broker:
    image: redis:7-alpine
  worker:
    build: .
    env_file: .env
    depends_on:
    - broker
    - qdrant
    command: celery -A api.celery_app worker --loglevel=info --concurrency=1
    volumes:
      - .:/app
      - uploads_data:/data        
      - models_cache:/root/.cache/huggingface 
volumes:
  qdrant_data:
  uploads_data:
  models_cache: