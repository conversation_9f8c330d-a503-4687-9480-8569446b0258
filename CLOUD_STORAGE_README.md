# Cloud Storage Implementation

This document describes the cloud storage versions of the upload-related files that have been created as duplicates of the original local volume storage implementation.

## Overview

The cloud storage implementation provides the same functionality as the original local volume storage system but stores files in cloud storage buckets (AWS S3, Google Cloud Storage) instead of local volumes.

## Files Created

### 1. Core Cloud Storage Module
- **`api/services/cloud_storage.py`** - Main cloud storage utility module
  - Provides abstract interface for cloud storage operations
  - Implements AWS S3 and Google Cloud Storage support
  - Factory function to get configured cloud storage instance
  - Utility functions for generating cloud storage keys

### 2. Cloud-Based Routers
- **`api/routers/upload_cloud.py`** - Cloud version of `upload.py`
  - Endpoint: `/upload-cloud/video/` and `/upload-cloud/doc/`
  - Uploads files to cloud storage instead of local volume
  - Uses cloud-based Celery tasks

- **`api/routers/video_cloud.py`** - Cloud version of `video.py`
  - Endpoint: `/videos-cloud/{video_id}/` and `/videos-cloud/{video_id}/info/`
  - Streams videos from cloud storage with HTTP Range support
  - Downloads files temporarily for streaming (can be optimized)

### 3. Cloud-Based Services
- **`api/services/transcribe_cloud.py`** - Cloud version of `transcribe.py`
  - Downloads video from cloud storage for transcription
  - Adds cloud_key to metadata for reference
  - Cleans up temporary files after processing

- **`api/services/doc_ingest_cloud.py`** - Cloud version of `doc_ingest.py`
  - Downloads documents from cloud storage for processing
  - Adds cloud_key to metadata for reference
  - Cleans up temporary files after processing

### 4. Cloud-Based Tasks
- **`api/tasks_cloud.py`** - Cloud version of `tasks.py`
  - `transcribe_task_cloud` - Transcribes videos from cloud storage
  - `doc_ingestion_task_cloud` - Processes documents from cloud storage
  - `video_mcq_task_cloud` and `doc_mcq_task_cloud` - Generate MCQs (same as original)

## Configuration

### Environment Variables

Add the following to your `.env` file (see updated `example.env`):

```bash
# Cloud Storage Configuration
CLOUD_STORAGE_PROVIDER=s3  # or 'gcs'

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GCS_BUCKET=your-bucket-name
```

### Dependencies

For AWS S3 support:
```bash
pip install boto3
```

For Google Cloud Storage support:
```bash
pip install google-cloud-storage
```

## Usage

### 1. Upload Video to Cloud Storage
```bash
curl -X POST "http://localhost:8000/upload-cloud/video/" \
  -H "X-API-Key: your-api-key" \
  -F "file=@video.mp4" \
  -F "video_id=test-video-123"
```

### 2. Upload Document to Cloud Storage
```bash
curl -X POST "http://localhost:8000/upload-cloud/doc/" \
  -H "X-API-Key: your-api-key" \
  -F "file=@document.pdf" \
  -F "doc_id=test-doc-123"
```

### 3. Stream Video from Cloud Storage
```bash
curl "http://localhost:8000/videos-cloud/test-video-123/"
```

### 4. Get Video Info from Cloud Storage
```bash
curl "http://localhost:8000/videos-cloud/test-video-123/info/"
```

## Key Differences from Local Storage

1. **File Storage**: Files are stored in cloud buckets instead of `/data/uploads`
2. **File Access**: Files are downloaded temporarily for processing
3. **Metadata**: Cloud storage keys are added to Qdrant metadata
4. **Endpoints**: New endpoints with `-cloud` suffix to avoid conflicts
5. **Tasks**: New Celery tasks with `_cloud` suffix

## File Naming Convention

Cloud storage keys follow the pattern:
- Videos: `videos/{video_id}_{filename}`
- Documents: `documents/{doc_id}_{filename}`

## Limitations and Considerations

1. **Temporary Downloads**: Current implementation downloads entire files for processing
2. **Range Requests**: Video streaming downloads the entire file (can be optimized)
3. **File Discovery**: Video streaming tries common patterns to find files
4. **Cleanup**: Temporary files are cleaned up after processing

## Production Optimizations

For production use, consider:

1. **Streaming Optimization**: Use cloud provider's range request capabilities
2. **File Metadata Storage**: Store cloud keys in database for faster lookups
3. **Caching**: Implement local caching for frequently accessed files
4. **CDN Integration**: Use CloudFront (AWS) or Cloud CDN (GCP) for video streaming
5. **Presigned URLs**: Generate presigned URLs for direct client access

## Integration with Existing System

The cloud storage implementation is designed to coexist with the original local storage system:

- Original endpoints remain unchanged
- New cloud endpoints use different prefixes
- Same database tables and Qdrant collections are used
- Same MCQ generation logic is preserved

This allows for gradual migration or hybrid deployments where some files use local storage and others use cloud storage.
