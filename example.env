# FastAPI security (will change later)
API_KEY=my-api-key

# Embedding provider: openai | hf-api | hf-local | hf-endpoint | local (local not supported yet)
EMBED_PROVIDER=openai
EMBED_MODEL=text-embedding-3-small          # or sentence-transformers/all-MiniLM-L6-v2

# LLM provider: openai | hf | ollama | together (ollama not supported yet)
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini                       # or google/gemma-7b-it, mistral:7b, etc.

# Keys
OPENAI_API_KEY=
HF_API_TOKEN=
TOGETHER_API_KEY=

# Whisper
WHISPER_MODEL_SIZE=medium
WHISPER_DEVICE=auto        # cpu | cuda | auto

# Qdrant
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION=video_chunks

# Celery
CELERY_BROKER_URL=redis://broker:6379/0
CELERY_RESULT_BACKEND=redis://broker:6379/1

# Cloud Storage Configuration
# Provider: s3 | gcs (Google Cloud Storage)
CLOUD_STORAGE_PROVIDER=s3

# AWS S3 Configuration (when CLOUD_STORAGE_PROVIDER=s3)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# Google Cloud Storage Configuration (when CLOUD_STORAGE_PROVIDER=gcs)
GOOGLE_CLOUD_PROJECT=
GCS_BUCKET=
# Note: For GCS, you'll also need to set up authentication via:
# - Service account key file (GOOGLE_APPLICATION_CREDENTIALS environment variable)
# - Or use default credentials if running on Google Cloud Platform