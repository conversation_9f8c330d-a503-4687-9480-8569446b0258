## MnemosAI API Documentation

This is the documentation for the **MnemosAI** API: a FastAPI-based application designed for video-based Retrieval-Augmented Generation (RAG) systems.

## Folder Structure Overview

The project is organized into two main directories:

- **`routers/`**: Contains the API route definitions. Check the [`Router README file`](routers/README.md) for the documentation.
- **`services/`**: Contains the main logic and helper services. Check the [`Service README file/`](services/README.md) for the documentation.

Additional key files and their responsibilities are detailed below.

## Core Files

### `main.py`

Main entry point for the FastAPI application that powers the Video RAG API.

#### Features:

- **App Title**: `"Video RAG API"`

- **CORS Middleware**:

  - Allows cross-origin requests from any domain (`allow_origins=["*"]`)
  - Supports HTTP `POST`, `GET`, and `OPTIONS` methods.
  - Accepts all headers and supports credentials (e.g. cookies, auth tokens).

- **Routers Included** (from the [`routers/`](routers/) directory):

  - **`health.py`**
  - **`upload.py`**
  - **`ask.py`**
  - **`task_status.py`**
  - **`video.py`**

- **Logging**:

  - Configured to output INFO-level logs to `stdout` with timestamps and module names.

### `celery_app.py`

Configures the Celery application for background task processing:

- **App name**: `"video_rag"`
- **Broker**: Redis (`CELERY_BROKER_URL`)
- **Result Backend**: Redis (`CELERY_RESULT_BACKEND`)
- **Task module**: `api.tasks`
- Ensures JSON serialization for tasks and results.

### `tasks.py`

Defines background tasks for processing uploaded content using Celery.

#### Tasks

- **`transcribe_task(file_path: str, video_id: str)`**

  - **Celery task name**: `"transcribe_video"`
  - Transcribes the given video using Whisper and indexes the resulting transcript chunks into Qdrant.
  - Internally calls: `transcribe_and_index()` from `services.transcribe`.
  - **Returns**: A dictionary containing the task status and `video_id`.

- **`doc_ingestion_task(file_path: str, doc_id: str)`**

  - **Celery task name**: `"doc_ingestion"`
  - Processes the given document (e.g., PDF or plain text) and indexes its content into Qdrant.
  - Internally calls: `ingest_and_index_doc()` from `services.doc_ingest`.
  - **Returns**: A dictionary containing the task status and `doc_id`.

These tasks are registered with the Celery app defined in `celery_app.py`. For more details on when and how these tasks are invoked, see the documentation for `routers/upload.py`.

#### `transcribe_video`

- Task name: `"transcribe_video"`
- Parameters:
  - `file_path`: Path to the uploaded video file
  - `video_id`: Associated video ID
- Calls `transcribe_and_index()` from `services/transcribe.py`.
- Returns a dictionary with a completion status and `video_id` after transcription and indexing.

#### `doc_ingestion`

- Task name: `"doc_ingestion"`
- Parameters:

  - `file_path`: Path to the uploaded document
  - `doc_id`: Associated document ID

- Calls `ingest_and_index_doc()` from `services/doc_ingest.py`.
- Returns a dictionary with a completion status and `doc_id` after ingestion and indexing.

### `deps.py`

Defines a FastAPI dependency:

- `get_api_key()` verifies the `x-api-key` header.
- Compares it against the `API_KEY` environment variable.
- Raises `401 Unauthorized` if the keys do not match.

### `provider_factory.py`

Provides functions to load LLM and embedding models dynamically based on environment settings.

#### Function: `get_embedder()`

Returns a text embedding model instance based on:

- `EMBED_PROVIDER`
- `EMBED_MODEL`

**Supported Providers**:

- `openai` → `OpenAIEmbeddings`
- `together` → `TogetherEmbeddings`
- `hf-api` → Hugging Face Inference API (`HuggingFaceInferenceAPIEmbeddings`)
- `hf-local` → Local Sentence Transformer (`HuggingFaceEmbeddings`)
- `hf-endpoint` → Custom HF Inference Endpoint (`HuggingFaceEndpointEmbeddings`)

#### Function: `get_chat_model()`

Returns a chat model instance based on:

- `LLM_PROVIDER`
- `LLM_MODEL`

**Supported Providers**:

- `openai` → `ChatOpenAI`
- `hf` → Hugging Face-hosted models via custom endpoint (`ChatHuggingFace`)
- `together` → `ChatTogether`

Raises `ValueError` if provider is unknown or required environment variables are missing.
