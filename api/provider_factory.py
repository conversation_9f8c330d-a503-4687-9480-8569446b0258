import os
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_huggingface import ChatHuggingFace, HuggingFaceEndpoint
from langchain_together import ChatTogether
# from langchain_ollama import ChatOllama


from langchain_huggingface.embeddings import (
    HuggingFaceEmbeddings,           # local ST model
    HuggingFaceEndpointEmbeddings,   # custom HF endpoint
)
from langchain_community.embeddings import (
    HuggingFaceInferenceAPIEmbeddings,  
)
from langchain_together import TogetherEmbeddings


def get_embedder():
    provider = os.getenv("EMBED_PROVIDER", "openai").lower()
    model = os.getenv("EMBED_MODEL")

    if not model:
        raise ValueError("EMBED_MODEL must be set")

    # ── OpenAI ------------------------------------------------
    if provider == "openai":
        return OpenAIEmbeddings(model=model)

    # ── Together ------------------------------------------------
    if provider == "together":
        return TogetherEmbeddings(
            model=model,
        )

    # ── Hugging Face free Inference API ----------------------
    if provider == "hf-api":
        return HuggingFaceInferenceAPIEmbeddings(
            api_key=os.getenv("HF_API_TOKEN"),
            model_name=model,
        )

    # ── Local ST model --------------------------------------
    if provider == "hf-local":
        # Depends on the type of similarity search. Set True if you're using cosine similarity, for dot and euclidean use False
        encode_kwargs = {'normalize_embeddings': True} 

        return HuggingFaceEmbeddings(
            model_name=model,
            cache_folder="/data/models",
            encode_kwargs=encode_kwargs
        )

    # ── Custom HF Compliant Endpoint ------------
    if provider == "hf-endpoint":
        return HuggingFaceEndpointEmbeddings(
            endpoint_url=os.getenv("HF_ENDPOINT_URL"), 
            model_name=model,
            task="text-embedding",
            api_key=os.getenv("HF_API_TOKEN"),
        )

    raise ValueError(f"Unknown EMBED_PROVIDER: {provider}")

def get_chat_model():
    provider = os.getenv("LLM_PROVIDER", "openai").lower()
    model = os.getenv("LLM_MODEL")

    if provider == "openai":
        return ChatOpenAI(model=model)
    if provider == "hf":
        endpoint = HuggingFaceEndpoint(
            api_key=os.getenv("HF_API_TOKEN"),
            repo_id=model,
            task="text-generation",
            max_new_tokens=int(os.getenv("HF_MAX_TOKENS", 512)),
            temperature=float(os.getenv("HF_TEMPERATURE", 0)),
            do_sample=os.getenv("HF_DO_SAMPLE", "false").lower() == "true",
        )
        return ChatHuggingFace(llm=endpoint)
    if provider == "together":
        return ChatTogether(
            model=model,
            together_api_key=os.getenv("TOGETHER_API_KEY"),
            temperature=float(os.getenv("TG_TEMPERATURE", 0)),
            max_tokens=os.getenv("TG_MAX_TOKENS"),                  # None → model default
            timeout=os.getenv("TG_TIMEOUT"),                        # seconds or None
            max_retries=int(os.getenv("TG_MAX_RETRIES", 2)),
        )
    # # Just dormant anticipatory code. Not supported yet. This is for a self hosted model in a docker container named ollama
    # if provider == "ollama":
    #     return ChatOllama(base_url="http://ollama:11434", model=model)
    
    raise ValueError(f"Unknown LLM_PROVIDER: {provider}")
