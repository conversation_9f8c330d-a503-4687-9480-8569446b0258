from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import health, upload, ask, task_status, video, mcq
import logging, sys

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    stream=sys.stdout,
)

origins = [
    "http://localhost:3000",  
]


app = FastAPI(title="Video RAG API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],         # ["*"] for any origin (not for prod)
    allow_methods=["POST", "GET", "OPTIONS"],
    allow_headers=["*"],
    allow_credentials=True,        # needed if you send cookies
)

app.include_router(health.router)
app.include_router(upload.router)
app.include_router(ask.router)
app.include_router(mcq.router)      
app.include_router(task_status.router)
app.include_router(video.router)      

