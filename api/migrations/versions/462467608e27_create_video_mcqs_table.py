"""create video_mcqs table

Revision ID: 462467608e27
Revises: 5c7115e32104
Create Date: 2025-06-14 10:46:58.603170

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '462467608e27'
down_revision: Union[str, None] = '5c7115e32104'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('doc_mcq',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('doc_id', sa.Text(), nullable=False),
    sa.Column('mcqs', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('doc_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('doc_mcq')
    # ### end Alembic commands ###
