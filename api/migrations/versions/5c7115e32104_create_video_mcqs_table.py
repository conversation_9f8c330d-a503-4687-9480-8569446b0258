"""create video_mcqs table

Revision ID: 5c7115e32104
Revises: 
Create Date: 2025-06-09 21:23:10.702148

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c7115e32104'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('video_mcq',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.Text(), nullable=False),
    sa.Column('mcqs', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('video_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('video_mcq')
    # ### end Alembic commands ###
