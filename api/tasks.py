from pathlib import Path
import asyncio
import logging

from .celery_app import celery_app

# ── services ------------------------------------------------------------
from .services.transcribe import transcribe_and_index
from .services.doc_ingest import ingest_and_index_doc
from .services.mcq import generate_mcqs_for_video_to_store, generate_mcqs_for_doc_to_store

from api.models import DocMCQ

# ── DB (Supabase / Postgres) -------------------------------------------
from .db import SessionLocal
from .models import VideoMCQ

# Configure logger
logger = logging.getLogger(__name__)

# ────────────────────────────────────────────────────────────────────────
# 1. Transcription  ➜  indexes video chunks in Qdrant
# ────────────────────────────────────────────────────────────────────────
@celery_app.task(bind=True, name="transcribe_video")
def transcribe_task(self, file_path: str, video_id: str):
    """
    Transcribe a video file and index the resulting chunks in Qdrant.
    
    Args:
        file_path: Path to the video file
        video_id: Unique identifier for the video
        
    Returns:
        Dict with status and video_id
    """
    logger.info(f"[Task {self.request.id}] Starting transcription task for video_id={video_id}")
    logger.debug(f"[Task {self.request.id}] File path: {file_path}")
    
    try:
        transcribe_and_index(Path(file_path), video_id)
        logger.info(f"[Task {self.request.id}] Transcription completed successfully for video_id={video_id}")
        return {"status": "completed", "video_id": video_id}
    except Exception as e:
        logger.error(f"[Task {self.request.id}] Transcription failed for video_id={video_id}: {str(e)}", exc_info=True)
        raise


# ────────────────────────────────────────────────────────────────────────
# 2. Document ingestion  ➜  indexes PDF / PPTX chunks in Qdrant
# ────────────────────────────────────────────────────────────────────────
@celery_app.task(bind=True, name="doc_ingestion")
def doc_ingestion_task(self, file_path: str, doc_id: str):
    """
    Process a document file and index the resulting chunks in Qdrant.
    
    Args:
        file_path: Path to the document file
        doc_id: Unique identifier for the document
        
    Returns:
        Dict with status and doc_id
    """
    logger.info(f"[Task {self.request.id}] Starting document ingestion task for doc_id={doc_id}")
    logger.debug(f"[Task {self.request.id}] File path: {file_path}")
    
    try:
        ingest_and_index_doc(Path(file_path), doc_id)
        logger.info(f"[Task {self.request.id}] Document ingestion completed successfully for doc_id={doc_id}")
        return {"status": "completed", "doc_id": doc_id}
    except Exception as e:
        logger.error(f"[Task {self.request.id}] Document ingestion failed for doc_id={doc_id}: {str(e)}", exc_info=True)
        raise


# ────────────────────────────────────────────────────────────────────────
# 3. MCQ generation  ➜  creates quiz and stores in Postgres
# ────────────────────────────────────────────────────────────────────────
@celery_app.task(bind=True, name="generate_video_mcq")
def video_mcq_task(self, video_id: str):
    """
    Generate multiple-choice questions for the given video
    and persist them to the `video_mcq` table.
    
    Args:
        video_id: Unique identifier for the video
        
    Returns:
        Dict with status, video_id, and mcq_count
    """
    logger.info(f"[Task {self.request.id}] Starting MCQ generation task for video_id={video_id}")
    
    try:
        # 1. create MCQs (async LLM call wrapped in asyncio)
        logger.debug(f"[Task {self.request.id}] Generating MCQs for video_id={video_id}")
        mcq_payload = asyncio.run(generate_mcqs_for_video_to_store(video_id))
        logger.info(f"[Task {self.request.id}] Generated {len(mcq_payload['questions'])} MCQs for video_id={video_id}")

        # 2. save to Postgres (upsert)
        logger.debug(f"[Task {self.request.id}] Saving MCQs to database for video_id={video_id}")
        db = SessionLocal()
        try:
            row = VideoMCQ(video_id=video_id, mcqs=mcq_payload["questions"])
            db.merge(row)           # merge = insert or update on conflict
            db.commit()
            logger.info(f"[Task {self.request.id}] Successfully saved MCQs to database for video_id={video_id}")
        except Exception as db_error:
            logger.error(f"[Task {self.request.id}] Database error while saving MCQs for video_id={video_id}: {str(db_error)}", exc_info=True)
            db.rollback()
            raise
        finally:
            logger.debug(f"[Task {self.request.id}] Closing database connection")
            db.close()

        return {"status": "completed", "video_id": video_id, "mcq_count": len(mcq_payload["questions"])}
    except Exception as e:
        logger.error(f"[Task {self.request.id}] MCQ generation failed for video_id={video_id}: {str(e)}", exc_info=True)
        raise


@celery_app.task(bind=True, name="generate_doc_mcq")
def doc_mcq_task(self, doc_id: str):
    """
    Generate multiple-choice questions for the given document
    and persist them to the `doc_mcq` table.
    
    Args:
        doc_id: Unique identifier for the document
        
    Returns:
        Dict with status, doc_id, and mcq_count
    """
    logger.info(f"[Task {self.request.id}] Starting MCQ generation task for doc_id={doc_id}")
    
    try:
        # 1. Generate MCQs
        logger.debug(f"[Task {self.request.id}] Generating MCQs for doc_id={doc_id}")
        mcq_payload = asyncio.run(generate_mcqs_for_doc_to_store(doc_id))
        logger.info(f"[Task {self.request.id}] Generated {len(mcq_payload['questions'])} MCQs for doc_id={doc_id}")

        # 2. Save to database
        logger.debug(f"[Task {self.request.id}] Saving MCQs to database for doc_id={doc_id}")
        db = SessionLocal()
        try:
            db.merge(DocMCQ(doc_id=doc_id, mcqs=mcq_payload["questions"]))
            db.commit()
            logger.info(f"[Task {self.request.id}] Successfully saved MCQs to database for doc_id={doc_id}")
        except Exception as db_error:
            logger.error(f"[Task {self.request.id}] Database error while saving MCQs for doc_id={doc_id}: {str(db_error)}", exc_info=True)
            db.rollback()
            raise
        finally:
            logger.debug(f"[Task {self.request.id}] Closing database connection")
            db.close()

        return {"status": "completed", "doc_id": doc_id, "mcq_count": len(mcq_payload["questions"])}
    except Exception as e:
        logger.error(f"[Task {self.request.id}] MCQ generation failed for doc_id={doc_id}: {str(e)}", exc_info=True)
        raise
