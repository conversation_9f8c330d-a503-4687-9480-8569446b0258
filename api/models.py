from sqlalchemy import Column, Integer, Text, JSON, TIMESTAMP, func
from api.db import Base

class VideoMCQ(Base):
    __tablename__ = "video_mcq"
    id         = Column(Integer, primary_key=True)
    video_id   = Column(Text, unique=True, nullable=False)
    mcqs       = Column(JSON, nullable=False)        # list of MCQs
    created_at = Column(TIMESTAMP, server_default=func.now())

class DocMCQ(Base):
    __tablename__ = "doc_mcq"

    id         = Column(Integer, primary_key=True)
    doc_id     = Column(Text, unique=True, nullable=False)
    mcqs       = Column(JSON, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())