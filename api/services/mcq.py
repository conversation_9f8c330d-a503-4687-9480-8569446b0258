"""
Generate multiple-choice questions (MCQs) for a video on-demand.

Rule:
  • ≤15   chunks → 1 MCQ
  • 16-40 chunks → 3 MCQs
  • 41-100 chunks → 5 MCQs
  • >100  chunks → ceil(chunks/20), capped at 10 MCQs
"""

import logging
from qdrant_client.http import models as qmodels
from langchain_core.prompts import ChatPromptTemplate
from api.provider_factory import get_chat_model
from api.services.rag import qclient, VIDEO_COLLECTION, DOC_COLLECTION
from typing import List, TypedDict, Annotated

# Configure logger
logger = logging.getLogger(__name__)

chat = get_chat_model()

# ── structured output schema we expect back from LL<PERSON> ────────────────────
# ── schema ──────────────────────────────────────────────────────────────
class MCQ(TypedDict):
    question: str
    options: List[str]              # exactly 4
    answer: str                     # "A" / "B" / "C" / "D"
    explanation: str

class MCQResponse(TypedDict):
    questions: List[MCQ]

# --- prompt pipeline ----------------------------------------------------
MCQ_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("system",
         "Create {n_q} multiple-choice questions (MCQs) based ONLY on the "
         "information below.  Each MCQ must have 4 options A-D, an 'answer' "
         "letter, and an 'explanation'.  Return JSON with a single key "
         "'questions' whose value is the list of MCQs."),
        ("human", "{transcript}")
    ]
)

video_mcq_chain = MCQ_PROMPT | chat.with_structured_output(MCQResponse)

# --- main helper --------------------------------------------------------
async def generate_mcqs_for_video(video_id: str) -> dict:
    logger.info(f"Generating MCQs for video: {video_id}")
    texts = _all_chunks_text(video_id)
    if not texts:
        logger.warning(f"Video not found: {video_id}")
        raise ValueError("Video not found")

    n_q = _decide_num_questions(len(texts))
    logger.info(f"Video {video_id}: {len(texts)} chunks, generating {n_q} MCQs")
    transcript_text = "\n".join(texts)

    logger.debug(f"Invoking LLM chain for video {video_id}")
    result: MCQResponse = await video_mcq_chain.ainvoke(
        {"n_q": n_q, "transcript": transcript_text}
    )
    logger.info(f"Generated {len(result['questions'])} MCQs for video {video_id}")

    return {
        "video_id": video_id,
        "num_questions": len(result["questions"]),
        "questions": result["questions"],
    }
    
async def generate_mcqs_for_video_to_store(video_id: str) -> dict:
    logger.info(f"Generating MCQs for video to store: {video_id}")
    texts = _all_chunks_text(video_id)
    if not texts:
        logger.warning(f"Video not found: {video_id}")
        raise ValueError("Video not found")

    n_q = _decide_num_questions_to_store(len(texts))
    logger.info(f"Video {video_id}: {len(texts)} chunks, generating {n_q} MCQs for storage")
    transcript_text = "\n".join(texts)

    logger.debug(f"Invoking LLM chain for video {video_id} (storage)")
    result: MCQResponse = await video_mcq_chain.ainvoke(
        {"n_q": n_q, "transcript": transcript_text}
    )
    logger.info(f"Generated {len(result['questions'])} MCQs for video {video_id} (storage)")

    return {
        "video_id": video_id,
        "num_questions": len(result["questions"]),
        "questions": result["questions"],
    }


# ── utilities ───────────────────────────────────────────────────────────
def _decide_num_questions(chunks: int) -> int:
    if chunks <= 15:
        return 1
    if chunks <= 40:
        return 3
    if chunks <= 100:
        return 5
    return min(10, -(-chunks // 20))        # ceil(chunks/20), max 10

def _decide_num_questions_to_store(chunks: int) -> int:
    if chunks <= 15:
        return 3
    if chunks <= 40:
        return 9
    if chunks <= 100:
        return 15
    return min(30, -(-3*chunks // 20))  

def _all_chunks_text(video_id: str) -> List[str]:
    """Retrieve page_content ('text') for every chunk belonging to the video."""
    logger.debug(f"Retrieving all chunks for video: {video_id}")
    texts: List[str] = []
    next_offset = None

    while True:
        logger.debug(f"Scrolling Qdrant for video {video_id}, offset: {next_offset}")
        points, next_offset = qclient.scroll(
            VIDEO_COLLECTION,
            with_payload=["page_content"],
            limit=256,
            offset=next_offset,
            scroll_filter=qmodels.Filter(
                must=[
                    qmodels.FieldCondition(
                        key="metadata.video_id",
                        match=qmodels.MatchValue(value=video_id),
                    )
                ]
            ),
        )
        chunk_count = len(points)
        logger.debug(f"Retrieved {chunk_count} chunks for video {video_id}")
        texts.extend(p.payload["page_content"] for p in points)
        if next_offset is None:
            break
    
    logger.info(f"Total chunks retrieved for video {video_id}: {len(texts)}")
    return texts





# DOCUMENT MCQs

# ── prompt --------------------------------------------------------------
PROMPT = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            "Create {n_q} multiple-choice questions (MCQs) from the document "
            "text below. Each MCQ has 4 options A-D, the correct letter in "
            "'answer', and a short 'explanation'. Return JSON with key "
            "'questions'."
        ),
        ("human", "{text}")
    ]
)

doc_mcq_chain = PROMPT | chat.with_structured_output(MCQResponse)

# ── public helper -------------------------------------------------------
async def generate_mcqs_for_doc_to_store(doc_id: str) -> MCQResponse:
    logger.info(f"Generating MCQs for document to store: {doc_id}")
    texts = _all_doc_chunks(doc_id)
    if not texts:
        logger.warning(f"Document not found: {doc_id}")
        raise ValueError("Document not found")

    n_q = _decide_num_questions_to_store(len(texts))
    logger.info(f"Document {doc_id}: {len(texts)} chunks, generating {n_q} MCQs for storage")
    full_text = "\n".join(texts)

    logger.debug(f"Invoking LLM chain for document {doc_id} (storage)")
    result: MCQResponse = await doc_mcq_chain.ainvoke(
        {"n_q": n_q, "text": full_text}
    )
    logger.info(f"Generated {len(result['questions'])} MCQs for document {doc_id} (storage)")

    return {
        "doc_id": doc_id,
        "num_questions": len(result["questions"]),
        "questions": result["questions"],
    }
    
    
async def generate_mcqs_for_doc(doc_id: str) -> MCQResponse:
    logger.info(f"Generating MCQs for document: {doc_id}")
    texts = _all_doc_chunks(doc_id)
    if not texts:
        logger.warning(f"Document not found: {doc_id}")
        raise ValueError("Document not found")

    n_q = _decide_num_questions(len(texts))
    logger.info(f"Document {doc_id}: {len(texts)} chunks, generating {n_q} MCQs")
    full_text = "\n".join(texts)

    logger.debug(f"Invoking LLM chain for document {doc_id}")
    result: MCQResponse = await doc_mcq_chain.ainvoke(
        {"n_q": n_q, "text": full_text}
    )
    logger.info(f"Generated {len(result['questions'])} MCQs for document {doc_id}")

    return {
        "doc_id": doc_id,
        "num_questions": len(result["questions"]),
        "questions": result["questions"],
    }


def _all_doc_chunks(doc_id: str) -> List[str]:
    logger.debug(f"Retrieving all chunks for document: {doc_id}")
    texts, next_offset = [], None
    while True:
        logger.debug(f"Scrolling Qdrant for document {doc_id}, offset: {next_offset}")
        points, next_offset = qclient.scroll(
            DOC_COLLECTION,
            with_payload=["page_content"],
            limit=256,
            offset=next_offset,
            scroll_filter=qmodels.Filter(
                must=[qmodels.FieldCondition(
                    key="metadata.doc_id",
                    match=qmodels.MatchValue(value=doc_id)
                )]
            ),
        )
        chunk_count = len(points)
        logger.debug(f"Retrieved {chunk_count} chunks for document {doc_id}")
        texts.extend(p.payload["page_content"] for p in points)
        if next_offset is None:
            break
    
    logger.info(f"Total chunks retrieved for document {doc_id}: {len(texts)}")
    return texts

