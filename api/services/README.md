## `services/` Directory

Contains the core business logic and helper services:

```
├── services/
│   ├── rag.py
│   ├── transcribe.py
│   ├── doc_ingest.py
│   └── __init__.py

```

### `rag.py`

Implements Retrieval-Augmented Generation (RAG) logic with timestamp and page-based citation support.

Main Features:

- Connects to Qdrant vector store collections for video and document chunks, using environment variables:
  • `QDRANT_VIDEO_COLLECTION`
  • `QDRANT_DOC_COLLECTION`

- Loads the embedding model via `get_embedder()` and determines the embedding dimensionality dynamically.

- Sets up `QdrantVectorStore` instances (via LangChain integration) for both videos and documents.

Core Functionality:

- Performs vector similarity search over stored chunks using a user-provided question.
- Applies a secondary LLM-based relevance check (`structured_checker`) to keep only contextually useful chunks.
- Prepares the prompt with:
  • Timestamp ranges (`MM:SS-MM:SS`) for video-based citations.
  • Page numbers for document-based citations.

- Sends a formatted prompt to the chat model (from `get_chat_model()`) and generates an answer.

Return Structure:

- A `dict` containing:
  • `answer` – The generated response (`str`).
  • `citations` – A list of citations relevant to the answer: - For video: `{"start": float, "end": float}` - For document: `{"page": int}`

Public API Functions:

- `answer_video_question(video_id: str, question: str)` → RAG-based answer from a specific video's transcript.
- `answer_doc_question(doc_id: str, question: str)` → RAG-based answer from a specific document.
- `answer_combined_question(video_id: str, doc_id: str, question: str)` → RAG-based answer from both sources.
- `fallback_video_answer(question: str)` → Fallback answer from any video if the target video lacks relevant data.

Helper Utilities:

- `_has_points()` – Checks if a Qdrant collection contains chunks for a given `video_id` or `doc_id`.
- `_sec_to_ts()` – Converts seconds (float) to timestamp string (`MM:SS`).
- `_ensure_collection()` – Ensures a collection exists in Qdrant; creates one if it doesn’t.
- `_filter_relevant_docs()` – Filters out irrelevant chunks using an LLM with structured JSON output.

### `transcribe.py`

Handles the transcription, chunking, embedding, and indexing of video content into a Qdrant vector store:

- Loads the Whisper model via `faster-whisper`, using environment-defined configuration (`WHISPER_MODEL_SIZE`, `WHISPER_DEVICE`), to transcribe audio from video files.
- Processes Whisper segments into contiguous text chunks, avoiding mid-segment splits to preserve precise `start` and `end` timestamps.
- Applies a configurable overlap (`CHUNK_OVERLAP_SEC`, default: 3.0 seconds) between chunks to avoid truncating meaningful answers during retrieval.
- Stores metadata for each chunk, including:

  - `video_id`, `chunk_idx`, `start`, and `end` timestamps (in seconds).

- Computes embeddings for each chunk using the active embedding provider loaded via `get_embedder()`.
- Checks if the target Qdrant collection (`QDRANT_VIDEO_COLLECTION`, default: `video_chunks`) exists; creates it with `COSINE` distance and proper vector dimensionality if absent.
- Uploads all chunks and their metadata to Qdrant using `QdrantVectorStore.add_texts()` for fast vector indexing.
- Logs all key stages of the pipeline:

  - Model loading, segment count, chunking logic, embedding configuration, collection setup, and indexing completion.

### `doc_ingest.py`

Handles the ingestion and indexing of uploaded documents (`PDF`, `PPT`, `PPTX`), splitting content into manageable chunks and storing their embeddings in a Qdrant vector database:

- Supports `.pdf`, `.ppt`, and `.pptx` formats.
- Uses `PyPDFLoader` (from LangChain) to load and extract text per page for PDF files.
- Uses `python-pptx` to extract text from PowerPoint slides, combining all text elements into a single slide-level chunk.
- Applies recursive splitting via `RecursiveCharacterTextSplitter` when page/slide content exceeds a configurable limit (`PAGE_CHAR_LIMIT`, default: 1000 characters), preserving structure via paragraph → sentence → word fallback.
- Maintains metadata for each chunk:

  - `doc_id`, `filename`, `chunk_idx`, `page_num`, `doc_type`, and `sub_idx` (if split).

- Embeds the chunks using a dynamically loaded embedding model (`get_embedder()`), supporting multiple providers.
- Checks if the Qdrant collection (`QDRANT_DOC_COLLECTION`, default: `doc_chunks`) exists; creates it with `COSINE` distance and `on_disk_payload=True` if missing.
- Adds all chunks and their metadata to Qdrant via `QdrantVectorStore.add_texts()` (batch size: 64).
- Logs every major step in the pipeline, including extraction, embedding setup, collection check/creation, and indexing completion.

### `__init__.py`

Initializes the Python package.
