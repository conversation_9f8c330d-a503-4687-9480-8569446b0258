from pathlib import Path
import logging, os
from typing import Tuple, List, Dict, Any

from pptx import Presentation
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader

from api.provider_factory import get_embedder
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http.exceptions import UnexpectedResponse
from qdrant_client.http.models import Distance, VectorParams

# ─── env config ──────────────────────────────────────────────────────────
COLLECTION = os.getenv("QDRANT_DOC_COLLECTION", "doc_chunks")
CHAR_LIMIT = int(os.getenv("PAGE_CHAR_LIMIT", 1000))           # split threshold

qclient = QdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", "6333")),
)

splitter = RecursiveCharacterTextSplitter(
    chunk_size=CHAR_LIMIT,
    chunk_overlap=100,
    separators=["\n\n", ".", " ", ""],  # tries paragraph → sentence → word
)

# ─── extraction function ────────────────────────────────────────────────
def extract_one_chunk_per_page_or_slide(doc_path: str, doc_id: str) -> Tuple[List[str], List[Dict[str, Any]]]:
    """Extract text (+ sub-chunks if needed) and metadata per slide or page."""
    ext = Path(doc_path).suffix.lower()
    filename = Path(doc_path).name

    chunks: List[str] = []
    metas: List[Dict[str, Any]] = []
    chunk_idx = 0                                            # global counter

    def _append(text: str, page: int, sub: int | None):
        nonlocal chunk_idx
        chunks.append(text)
        meta = {
            "doc_id": doc_id,
            "filename": filename,
            "chunk_idx": chunk_idx,
            "page_num": page,
            "doc_type": ext,
        }
        if sub is not None:
            meta["sub_idx"] = sub
        metas.append(meta)
        chunk_idx += 1

    if ext in (".ppt", ".pptx"):
        prs = Presentation(doc_path)
        for page, slide in enumerate(prs.slides, start=1):
            texts = [s.text.strip() for s in slide.shapes if getattr(s, "text", "").strip()]
            combined = "\n".join(texts).strip()
            if not combined:
                continue                                      # skip empty slide
            if len(combined) <= CHAR_LIMIT:
                _append(combined, page, None)
            else:
                for sub, part in enumerate(splitter.split_text(combined)):
                    _append(part, page, sub)

    elif ext == ".pdf":
        docs = PyPDFLoader(doc_path).load()
        for page, doc in enumerate(docs, start=1):
            text = doc.page_content.strip()
            if not text:
                continue                                      # skip blank page
            if len(text) <= CHAR_LIMIT:
                _append(text, page, None)
            else:
                for sub, part in enumerate(splitter.split_text(text)):
                    _append(part, page, sub)

    else:
        raise ValueError("Unsupported file type")

    return chunks, metas

# ─── main worker ────────────────────────────────────────────────────────
def ingest_and_index_doc(doc_path: Path, doc_id: str) -> None:
    """
    Ingest and index a document: one chunk per page/slide,
    with optional recursive split when a page exceeds CHAR_LIMIT.
    """
    log = logging.getLogger("doc_ingestion")
    log.info(f"[{doc_id}] 🏁  START  — File = {doc_path}")

    chunks, metadatas = extract_one_chunk_per_page_or_slide(str(doc_path), doc_id)
    log.info(f"[{doc_id}]  Chunking done — {len(chunks)} chunks")

    embedder = get_embedder()
    dim = len(embedder.embed_query("test"))
    log.info(f"[{doc_id}]  Embeddings model ready (dim={dim})")

    try:
        qclient.get_collection(COLLECTION)
        log.info(f"[{doc_id}]  Collection '{COLLECTION}' already exists")
    except UnexpectedResponse:
        qclient.create_collection(
            COLLECTION,
            vectors_config=VectorParams(
                size=dim,
                distance=Distance.COSINE,
                on_disk_payload=True,      # ⬅️  store metadata off-RAM
            ),
        )
        log.info(f"[{doc_id}]  Collection '{COLLECTION}' created")

    log.info(f"[{doc_id}]  Upserting vectors …")
    store = QdrantVectorStore(
        client=qclient, collection_name=COLLECTION, embedding=embedder
    )
    store.add_texts(chunks, metadatas=metadatas, batch_size=64)   # batched insert
    log.info(f"[{doc_id}]  Indexed {len(chunks)} chunks — DONE")
