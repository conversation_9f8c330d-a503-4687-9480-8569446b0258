"""
Transcribe a video from cloud storage, chunk it with timestamps, embed, and upsert to Qdrant.
Each chunk is built from whole Whisper segments so we keep accurate
`start` / `end` seconds in the metadata for later citation.
"""

import os, logging
from pathlib import Path
from typing import List, Dict

from faster_whisper import WhisperModel
from api.provider_factory import get_embedder, get_chat_model
from api.services.cloud_storage import get_cloud_storage

from langchain_qdrant import QdrantVectorStore
from qdrant_client.http.exceptions import UnexpectedResponse
from qdrant_client.http.models import Distance, VectorParams
from qdrant_client import QdrantClient, models as qmodels   

# ─── env config ──────────────────────────────────────────────────────────
WHISPER_MODEL_SIZE = os.getenv("WHISPER_MODEL_SIZE", "medium")
WHISPER_DEVICE = os.getenv("WHISPER_DEVICE", "auto")
COLLECTION = os.getenv("QDRANT_VIDEO_COLLECTION", "video_chunks")
CHUNK_CHAR_LIMIT = 300
CHUNK_OVERLAP_SEC = 3.0  # seconds of overlap to avoid answer cut‑offs

qclient = QdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", "6333")),
)

# ─── main worker ─────────────────────────────────────────────────────────
def transcribe_and_index_cloud(cloud_key: str, video_id: str) -> None:
    """
    Download video from cloud storage, transcribe it, chunk it with timestamps, 
    embed, and upsert to Qdrant.
    """
    log = logging.getLogger("video-rag-cloud")
    log.info(f"[{video_id}] 🏁  START  — Cloud Key = {cloud_key}")

    # 1. Download video from cloud storage
    cloud_storage = get_cloud_storage()
    
    try:
        # Download to temporary local file
        local_video_path = cloud_storage.download_file(cloud_key)
        log.info(f"[{video_id}] 📥  Downloaded from cloud storage to {local_video_path}")
        
        # 2. Whisper transcription (streaming)
        model = WhisperModel(
            WHISPER_MODEL_SIZE, device=WHISPER_DEVICE, compute_type="int8"
        )
        log.info(f"[{video_id}] 🔊  Whisper model loaded ({WHISPER_MODEL_SIZE}, {WHISPER_DEVICE})")
        segments, _ = model.transcribe(local_video_path)

        # 3. Build chunks keeping timestamps
        chunks: List[str] = []
        metadatas: List[Dict] = []

        buf_text, buf_start, buf_end = "", None, None
        count = 0
        for seg in segments:
            count += 1
            if buf_start is None:
                buf_start = float(seg.start)

            tentative = f"{buf_text} {seg.text.strip()}".strip()
            if len(tentative) > CHUNK_CHAR_LIMIT and buf_text:
                metadatas.append(
                    {
                        "video_id": video_id,
                        "chunk_idx": len(chunks),
                        "start": buf_start,
                        "end": buf_end + CHUNK_OVERLAP_SEC,
                        "cloud_key": cloud_key,  # Add cloud storage reference
                    }
                )
                chunks.append(buf_text.strip())

                buf_text = seg.text.strip()
                buf_start = max(0.0, float(seg.start) - CHUNK_OVERLAP_SEC)
            else:
                buf_text = tentative

            buf_end = float(seg.end)

        log.info(f"[{video_id}]  Transcription finished — {count} segments")

        # flush last chunk
        if buf_text:
            metadatas.append(
                {
                    "video_id": video_id,
                    "chunk_idx": len(chunks),
                    "start": buf_start,
                    "end": buf_end,
                    "cloud_key": cloud_key,  # Add cloud storage reference
                }
            )
            chunks.append(buf_text.strip())

        log.info(f"[{video_id}]  Chunking done — {len(chunks)} chunks")

        # 4. Embeddings
        embedder = get_embedder()

        dim = len(embedder.embed_query("test"))
        log.info(f"[{video_id}]  Embeddings model ready (dim={dim})")

        # 5. Ensure collection exists
        try:
            qclient.get_collection(COLLECTION)
            log.info(f"[{video_id}]  Collection '{COLLECTION}' already exists")
        except UnexpectedResponse:
            qclient.create_collection(
                COLLECTION,
                vectors_config=VectorParams(size=dim, distance=Distance.COSINE),
            )
            log.info(f"[{video_id}]  Collection '{COLLECTION}' created")

        # 6. Upsert
        log.info(f"[{video_id}]  Upserting vectors …")
        store = QdrantVectorStore(
            client=qclient, collection_name=COLLECTION, embedding=embedder
        )
        store.add_texts(chunks, metadatas=metadatas)
        log.info(f"[{video_id}]  Indexed {len(chunks)} chunks — DONE")
        
    except Exception as e:
        log.error(f"[{video_id}] Error during transcription from cloud storage: {str(e)}")
        raise
    finally:
        # Clean up temporary file
        try:
            if 'local_video_path' in locals():
                os.unlink(local_video_path)
                log.info(f"[{video_id}] 🗑️  Cleaned up temporary file {local_video_path}")
        except Exception as e:
            log.warning(f"[{video_id}] Failed to clean up temporary file: {str(e)}")
