"""
Cloud storage utility module for handling file uploads and downloads.
Supports multiple cloud providers: AWS S3, Google Cloud Storage, Azure Blob Storage.
"""

import os
import logging
from pathlib import Path
from typing import Optional, BinaryIO, Union
from abc import ABC, abstractmethod
import tempfile

logger = logging.getLogger(__name__)


class CloudStorageInterface(ABC):
    """Abstract interface for cloud storage operations."""
    
    @abstractmethod
    def upload_file(self, file_obj: BinaryIO, key: str) -> str:
        """Upload a file to cloud storage and return the storage URL/path."""
        pass
    
    @abstractmethod
    def download_file(self, key: str, local_path: Optional[str] = None) -> str:
        """Download a file from cloud storage to local path. Returns local file path."""
        pass
    
    @abstractmethod
    def get_file_url(self, key: str) -> str:
        """Get a URL for accessing the file."""
        pass
    
    @abstractmethod
    def delete_file(self, key: str) -> bool:
        """Delete a file from cloud storage."""
        pass
    
    @abstractmethod
    def file_exists(self, key: str) -> bool:
        """Check if a file exists in cloud storage."""
        pass
    
    @abstractmethod
    def get_file_size(self, key: str) -> int:
        """Get the size of a file in bytes."""
        pass


class S3CloudStorage(CloudStorageInterface):
    """AWS S3 cloud storage implementation."""
    
    def __init__(self, bucket_name: str, region: str = "us-east-1"):
        self.bucket_name = bucket_name
        self.region = region
        self._client = None
    
    @property
    def client(self):
        if self._client is None:
            try:
                import boto3
                self._client = boto3.client(
                    's3',
                    region_name=self.region,
                    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
                )
            except ImportError:
                raise ImportError("boto3 is required for S3 storage. Install with: pip install boto3")
        return self._client
    
    def upload_file(self, file_obj: BinaryIO, key: str) -> str:
        """Upload file to S3 bucket."""
        try:
            self.client.upload_fileobj(file_obj, self.bucket_name, key)
            logger.info(f"Successfully uploaded {key} to S3 bucket {self.bucket_name}")
            return f"s3://{self.bucket_name}/{key}"
        except Exception as e:
            logger.error(f"Failed to upload {key} to S3: {str(e)}")
            raise
    
    def download_file(self, key: str, local_path: Optional[str] = None) -> str:
        """Download file from S3 to local path."""
        if local_path is None:
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            local_path = temp_file.name
            temp_file.close()
        
        try:
            self.client.download_file(self.bucket_name, key, local_path)
            logger.info(f"Successfully downloaded {key} from S3 to {local_path}")
            return local_path
        except Exception as e:
            logger.error(f"Failed to download {key} from S3: {str(e)}")
            raise
    
    def get_file_url(self, key: str) -> str:
        """Get S3 URL for the file."""
        return f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{key}"
    
    def delete_file(self, key: str) -> bool:
        """Delete file from S3."""
        try:
            self.client.delete_object(Bucket=self.bucket_name, Key=key)
            logger.info(f"Successfully deleted {key} from S3")
            return True
        except Exception as e:
            logger.error(f"Failed to delete {key} from S3: {str(e)}")
            return False
    
    def file_exists(self, key: str) -> bool:
        """Check if file exists in S3."""
        try:
            self.client.head_object(Bucket=self.bucket_name, Key=key)
            return True
        except:
            return False
    
    def get_file_size(self, key: str) -> int:
        """Get file size from S3."""
        try:
            response = self.client.head_object(Bucket=self.bucket_name, Key=key)
            return response['ContentLength']
        except Exception as e:
            logger.error(f"Failed to get size for {key} from S3: {str(e)}")
            raise


class GCSCloudStorage(CloudStorageInterface):
    """Google Cloud Storage implementation."""
    
    def __init__(self, bucket_name: str, project_id: Optional[str] = None):
        self.bucket_name = bucket_name
        self.project_id = project_id or os.getenv('GOOGLE_CLOUD_PROJECT')
        self._client = None
        self._bucket = None
    
    @property
    def client(self):
        if self._client is None:
            try:
                from google.cloud import storage
                self._client = storage.Client(project=self.project_id)
                self._bucket = self._client.bucket(self.bucket_name)
            except ImportError:
                raise ImportError("google-cloud-storage is required for GCS. Install with: pip install google-cloud-storage")
        return self._client
    
    @property
    def bucket(self):
        if self._bucket is None:
            _ = self.client  # Initialize client and bucket
        return self._bucket
    
    def upload_file(self, file_obj: BinaryIO, key: str) -> str:
        """Upload file to GCS bucket."""
        try:
            blob = self.bucket.blob(key)
            file_obj.seek(0)  # Reset file pointer
            blob.upload_from_file(file_obj)
            logger.info(f"Successfully uploaded {key} to GCS bucket {self.bucket_name}")
            return f"gs://{self.bucket_name}/{key}"
        except Exception as e:
            logger.error(f"Failed to upload {key} to GCS: {str(e)}")
            raise
    
    def download_file(self, key: str, local_path: Optional[str] = None) -> str:
        """Download file from GCS to local path."""
        if local_path is None:
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            local_path = temp_file.name
            temp_file.close()
        
        try:
            blob = self.bucket.blob(key)
            blob.download_to_filename(local_path)
            logger.info(f"Successfully downloaded {key} from GCS to {local_path}")
            return local_path
        except Exception as e:
            logger.error(f"Failed to download {key} from GCS: {str(e)}")
            raise
    
    def get_file_url(self, key: str) -> str:
        """Get GCS URL for the file."""
        return f"https://storage.googleapis.com/{self.bucket_name}/{key}"
    
    def delete_file(self, key: str) -> bool:
        """Delete file from GCS."""
        try:
            blob = self.bucket.blob(key)
            blob.delete()
            logger.info(f"Successfully deleted {key} from GCS")
            return True
        except Exception as e:
            logger.error(f"Failed to delete {key} from GCS: {str(e)}")
            return False
    
    def file_exists(self, key: str) -> bool:
        """Check if file exists in GCS."""
        try:
            blob = self.bucket.blob(key)
            return blob.exists()
        except:
            return False
    
    def get_file_size(self, key: str) -> int:
        """Get file size from GCS."""
        try:
            blob = self.bucket.blob(key)
            blob.reload()
            return blob.size
        except Exception as e:
            logger.error(f"Failed to get size for {key} from GCS: {str(e)}")
            raise


def get_cloud_storage() -> CloudStorageInterface:
    """Factory function to get the configured cloud storage instance."""
    provider = os.getenv("CLOUD_STORAGE_PROVIDER", "s3").lower()
    
    if provider == "s3":
        bucket_name = os.getenv("AWS_S3_BUCKET")
        if not bucket_name:
            raise ValueError("AWS_S3_BUCKET environment variable is required for S3 storage")
        region = os.getenv("AWS_REGION", "us-east-1")
        return S3CloudStorage(bucket_name, region)
    
    elif provider == "gcs":
        bucket_name = os.getenv("GCS_BUCKET")
        if not bucket_name:
            raise ValueError("GCS_BUCKET environment variable is required for GCS storage")
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        return GCSCloudStorage(bucket_name, project_id)
    
    else:
        raise ValueError(f"Unsupported cloud storage provider: {provider}")


def generate_cloud_key(file_id: str, filename: str, file_type: str = "uploads") -> str:
    """Generate a standardized key for cloud storage."""
    # Clean filename to be cloud storage safe
    safe_filename = "".join(c for c in filename if c.isalnum() or c in "._-")
    return f"{file_type}/{file_id}_{safe_filename}"
