"""
Retrieval-Augmented Generation with timestamp or page citations.
Supports:
  • Video-only answering          → answer_video_question
  • Document-only answering       → answer_doc_question
  • Combined video + document     → answer_combined_question
"""

import os
import logging
from typing import List, TypedDict, Annotated, Literal

from api.provider_factory import get_embedder, get_chat_model
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.documents import Document

from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient, models as qmodels
from qdrant_client.http.exceptions import UnexpectedResponse
from qdrant_client.http.models import VectorParams, Distance, Filter, FieldCondition, MatchValue

# Configure logger
logger = logging.getLogger(__name__)

# ── collections ─────────────────────────────────────────────────────────
VIDEO_COLLECTION = os.getenv("QDRANT_VIDEO_COLLECTION", "video_chunks")
DOC_COLLECTION   = os.getenv("QDRANT_DOC_COLLECTION",   "doc_chunks")

qclient = QdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", "6333")),
)

embedder = get_embedder()
dim = len(embedder.embed_query("test"))

def _ensure_collection(name: str):
    try:
        qclient.get_collection(name)
        logger.debug(f"Collection '{name}' already exists")
    except UnexpectedResponse:
        logger.info(f"Creating collection '{name}' with dimension {dim}")
        qclient.create_collection(
            name,
            vectors_config=VectorParams(size=dim, distance=Distance.COSINE),
        )
        logger.info(f"Collection '{name}' created successfully")

logger.info(f"Ensuring collections exist: {VIDEO_COLLECTION}, {DOC_COLLECTION}")
_ensure_collection(VIDEO_COLLECTION)
_ensure_collection(DOC_COLLECTION)

# stores
logger.info("Initializing vector stores with embedder")
video_store = QdrantVectorStore(client=qclient, collection_name=VIDEO_COLLECTION, embedding=embedder)
doc_store   = QdrantVectorStore(client=qclient, collection_name=DOC_COLLECTION,   embedding=embedder)

chat = get_chat_model()
logger.info("Chat model initialized")

# ── prompts (unchanged) ─────────────────────────────────────────────────
PROMPT = ChatPromptTemplate.from_messages(
    [
        ("system",
         "Answer the question using ONLY the provided context. "
         "Do NOT mention the context or say that the answer is based on the context. "
         "For each fact, include either a time range (MM:SS-MM:SS) or a page number in parentheses."),
        ("human", "Context:\n{context}\n\nQuestion: {question}")
    ]
)

class RelevanceCheck(TypedDict):
    is_relevant: Annotated[bool, "True if chunk helps answer."]

EVALUATION_PROMPT = ChatPromptTemplate.from_messages(
    [
        ("system",
         "You're an AI assistant deciding if a context chunk contains relevant information to answer a specific question. "
         "Return structured JSON."),
        ("human", "Question: {question}\n\nChunk:\n{chunk}")
    ]
)
structured_checker = EVALUATION_PROMPT | chat.with_structured_output(RelevanceCheck)



class SourceChoice(TypedDict):
    source: Annotated[Literal["video", "doc", "both"],
                     'Return exactly "video", "doc", or "both".']

CLASSIFIER_PROMPT = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            (
                "You are an AI source classifier.\n\n"
                "Return **only one** token — \"video\", \"doc\", or \"both\" — "
                "based on these rules:\n"
                "  • \"video\" → **only** when the question clearly points to video-specific material, "
                "    e.g. it mentions a lecture, a scene, a timestamp, narration, or asks "
                "    \"where in the video…\".\n"
                "  • \"doc\"   → **only** when the question clearly points to document-specific material, "
                "    e.g. it mentions a page number, paragraph, section heading, table, or says "
                "    \"in the document…\".\n"
                "  • \"both\"  → every other time (this is the default when the prompt gives no explicit cue).\n\n"
                "If there is any doubt whatsoever, return \"both\"."
            ),
        ),
        ("human", "Question: {question}"),
    ]
)

source_classifier = CLASSIFIER_PROMPT | chat.with_structured_output(SourceChoice)

# ── public API functions ────────────────────────────────────────────────
async def fallback_video_answer(question: str) -> dict:
    """
    Search **all** videos for relevant chunks, then answer using those
    chunks.  Includes *every* unique video_id that contributed evidence,
    not just the first one.
    """
    logger.info(f"Generating fallback video answer for question: '{question[:50]}…'")
    all_docs = video_store.similarity_search(question, k=5)
    logger.debug(f"Retrieved {len(all_docs)} potential video chunks")

    relevant = await _filter_relevant_docs(all_docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant video chunks")

    if not relevant:
        logger.warning("No relevant video chunks found for fallback")
        return {
            "answer": "Sorry, no video contains the relevant information"
        }

    # collect **all** unique video IDs that supplied evidence
    vid_ids = {d.metadata.get("video_id", "unknown") for d in relevant}
    ids_str = ", ".join(sorted(vid_ids))
    logger.info(f"Using fallback video(s): {ids_str}")

    base_answer = _llm_answer(relevant, question, kind="video")

    return {
        "answer": (
            "I couldn't find relevant information in the requested video, "
            f"but found it in other video(s) ({ids_str}): {base_answer['answer']}"
        ),
        "citations": base_answer["citations"],
    }

    
async def fallback_doc_answer(question: str) -> dict:
    """
    Search **all** documents for relevant chunks, then answer using those
    chunks.  Includes every unique doc_id that contributed evidence, not
    just the first one.
    """
    logger.info(f"Generating fallback document answer for question: '{question[:50]}…'")
    all_docs = doc_store.similarity_search(question, k=5)
    logger.debug(f"Retrieved {len(all_docs)} potential document chunks")

    relevant = await _filter_relevant_docs(all_docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant document chunks")

    if not relevant:
        logger.warning("No relevant document chunks found for fallback")
        return {
            "answer": "Sorry, no doc contains the relevant information"
        }

    # collect **all** unique document IDs that supplied evidence
    doc_ids = {d.metadata.get("doc_id", "unknown") for d in relevant}
    ids_str = ", ".join(sorted(doc_ids))
    logger.info(f"Using fallback document(s): {ids_str}")

    base_answer = _llm_answer(relevant, question, kind="doc")

    return {
        "answer": (
            "I couldn't find relevant information in the requested document, "
            f"but found it in other document(s) ({ids_str}): {base_answer['answer']}"
        ),
        "citations": base_answer["citations"],
    }

    
async def answer_video_question_with_fallback(video_id: str, question: str) -> dict:
    logger.info(f"Answering question with fallback for video {video_id}: '{question[:50]}...'")
    
    if not _has_points(VIDEO_COLLECTION, "video_id", video_id):
        logger.warning(f"Video {video_id} not found")
        raise ValueError(f"Video {video_id} not found")
        
    logger.debug(f"Searching for relevant chunks in video {video_id}")
    docs = video_store.similarity_search(
        question, k=5,
        filter=Filter(must=[FieldCondition(key="metadata.video_id",
                                           match=MatchValue(value=video_id))])
    )
    logger.debug(f"Retrieved {len(docs)} potential chunks from video {video_id}")
    
    relevant = await _filter_relevant_docs(docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant chunks from video {video_id}")
    
    if relevant:
        logger.info(f"Found relevant information in video {video_id}")
        return _llm_answer(relevant, question, kind="video")

    logger.info(f"No relevant information in video {video_id}, using fallback")
    return await fallback_video_answer(question)

async def answer_video_question(video_id: str, question: str) -> dict:
    logger.info(f"Answering question for video {video_id}: '{question[:50]}...'")
    
    if not _has_points(VIDEO_COLLECTION, "video_id", video_id):
        logger.warning(f"Video {video_id} not found")
        raise ValueError(f"Video {video_id} not found")
        
    logger.debug(f"Searching for relevant chunks in video {video_id}")
    docs = video_store.similarity_search(
        question, k=5,
        filter=Filter(must=[FieldCondition(key="metadata.video_id",
                                           match=MatchValue(value=video_id))])
    )
    logger.debug(f"Retrieved {len(docs)} potential chunks from video {video_id}")
    
    relevant = await _filter_relevant_docs(docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant chunks from video {video_id}")
    
    return _llm_answer(relevant, question, kind="video")

async def answer_doc_question(doc_id: str, question: str) -> dict:
    logger.info(f"Answering question for document {doc_id}: '{question[:50]}...'")
    
    if not _has_points(DOC_COLLECTION, "doc_id", doc_id):
        logger.warning(f"Document {doc_id} not found")
        raise ValueError(f"Doc {doc_id} not found")
        
    logger.debug(f"Searching for relevant chunks in document {doc_id}")
    docs = doc_store.similarity_search(
        question, k=5,
        filter=Filter(must=[FieldCondition(key="metadata.doc_id",
                                           match=MatchValue(value=doc_id))])
    )
    logger.debug(f"Retrieved {len(docs)} potential chunks from document {doc_id}")
    
    relevant = await _filter_relevant_docs(docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant chunks from document {doc_id}")
    
    return _llm_answer(relevant, question, kind="doc")

async def answer_doc_question_with_fallback(doc_id: str, question: str) -> dict:
    logger.info(f"Answering question with fallback for document {doc_id}: '{question[:50]}...'")
    
    if not _has_points(DOC_COLLECTION, "doc_id", doc_id):
        logger.warning(f"Document {doc_id} not found")
        raise ValueError(f"Document {doc_id} not found")
        
    logger.debug(f"Searching for relevant chunks in document {doc_id}")
    docs = doc_store.similarity_search(
        question, k=5,
        filter=Filter(must=[FieldCondition(key="metadata.doc_id",
                                           match=MatchValue(value=doc_id))])
    )
    logger.debug(f"Retrieved {len(docs)} potential chunks from document {doc_id}")
    
    relevant = await _filter_relevant_docs(docs, question)
    logger.debug(f"Filtered to {len(relevant)} relevant chunks from document {doc_id}")
    
    if relevant:
        logger.info(f"Found relevant information in document {doc_id}")
        return _llm_answer(relevant, question, kind="doc")

    logger.info(f"No relevant information in document {doc_id}, using fallback")
    return await fallback_doc_answer(question)

# ── UPDATED combined answer function ────────────────────────────────────

async def answer_combined_question(video_id: str, doc_id: str, question: str) -> dict:
    logger.info(f"Answering combined question for video {video_id} and document {doc_id}: '{question[:50]}...'")
    
    # 1. Ask the classifier which source seems best
    logger.debug("Classifying preferred source for question")
    choice: SourceChoice = await source_classifier.ainvoke({"question": question})
    preferred = choice["source"]            # "video", "doc", or "both"
    logger.info(f"Classifier chose '{preferred}' as preferred source")

    async def _search(kind: str):
        logger.debug(f"Searching for relevant chunks in {kind}")
        if kind == "video":
            docs = video_store.similarity_search(
                question,
                k=5,
                filter=Filter(
                    must=[FieldCondition(key="metadata.video_id",
                                         match=MatchValue(value=video_id))]
                ),
            )
        else:  # "doc"
            docs = doc_store.similarity_search(
                question,
                k=5,
                filter=Filter(
                    must=[FieldCondition(key="metadata.doc_id",
                                         match=MatchValue(value=doc_id))]
                ),
            )
        logger.debug(f"Retrieved {len(docs)} potential chunks from {kind}")
        relevant = await _filter_relevant_docs(docs, question)
        logger.debug(f"Filtered to {len(relevant)} relevant chunks from {kind}")
        return relevant

    # ── CASE 1: preferred == "both" ────────────────────────────────────
    if preferred == "both":
        logger.debug("Using both sources for answer")
        vid_hits  = await _search("video")
        doc_hits  = await _search("doc")
        merged    = vid_hits + doc_hits
        if merged:
            logger.info(f"Found {len(merged)} relevant chunks from combined sources")
            return _llm_answer(merged, question, kind="mixed")
        
        logger.warning("No relevant information found in either source")
        return {
            "answer": "Sorry, I couldn't find relevant information in either the video or the document.",
            "citations": [],
        }

    # ── CASE 2: preferred is single source, with fallback ─────────────
    logger.debug(f"Searching preferred source: {preferred}")
    primary_hits = await _search(preferred)
    if primary_hits:
        logger.info(f"Found relevant information in preferred source: {preferred}")
        return _llm_answer(primary_hits, question, kind=preferred)

    # fallback to the other source
    fallback_kind = "doc" if preferred == "video" else "video"
    logger.info(f"No relevant information in {preferred}, trying fallback to {fallback_kind}")
    fallback_hits = await _search(fallback_kind)
    if fallback_hits:
        logger.info(f"Found relevant information in fallback source: {fallback_kind}")
        preface = (
            f"I couldn't find the information in the {preferred}, "
            f"but I found it in the {fallback_kind}. "
        )
        result = _llm_answer(fallback_hits, question, kind=fallback_kind)
        result["answer"] = preface + result["answer"]
        return result

    # neither store had useful chunks
    logger.warning("No relevant information found in either source")
    return {
        "answer": "Sorry, I couldn't find relevant information in either the video or the document.",
        "citations": [],
    }

async def answer_combined_question_with_fallback(
    video_id: str,
    doc_id: str,
    question: str,
) -> dict:
    """
    Answer a question using (in order of preference):

    1. Chunks from the requested video **and**/or document, depending on the LLM
       classifier’s choice.
    2. Chunks from *any other* video / document of the preferred kind.
    3. (Only when the classifier picked a single preferred kind) chunks from the
       opposite store (requested ID first, then any other).
    4. Apologise if nothing is relevant anywhere.

    Each answer is prefaced when it comes from outside the originally-requested
    source so the caller knows why a different video / doc was used.
    """
    logger.info(f"Answering combined question with fallback for video {video_id} and document {doc_id}: '{question[:50]}...'")

    # ── 0. Ask the LLM which source looks best ──────────────────────────
    logger.debug("Classifying preferred source for question")
    choice: SourceChoice = await source_classifier.ainvoke({"question": question})
    preferred = choice["source"]                 # "video", "doc", or "both"
    logger.info(f"Classifier chose '{preferred}' as preferred source")

    # ── helper search routine ───────────────────────────────────────────
    async def _search(kind: str, scope: str = "primary"):
        """
        kind   → "video" | "doc"
        scope  → "primary"  = only the requested ID
                 "all"      = any other item (excludes the requested ID)
        """
        logger.debug(f"Searching {kind} with scope '{scope}'")
        if kind == "video":
            store   = video_store
            m_key   = "metadata.video_id"
            m_value = video_id
        else:
            store   = doc_store
            m_key   = "metadata.doc_id"
            m_value = doc_id

        # Build the filter
        if scope == "primary":
            flt = Filter(
                must=[FieldCondition(key=m_key, match=MatchValue(value=m_value))]
            )
        else:  # scope == "all"
            flt = Filter(
                must_not=[FieldCondition(key=m_key, match=MatchValue(value=m_value))]
            )

        docs = store.similarity_search(question, k=5, filter=flt)
        logger.debug(f"Retrieved {len(docs)} potential chunks from {kind} ({scope})")
        relevant = await _filter_relevant_docs(docs, question)
        logger.debug(f"Filtered to {len(relevant)} relevant chunks from {kind} ({scope})")
        return relevant

    # ── 1️⃣  Search inside the requested ID(s) ───────────────────────────
    if preferred == "both":
        logger.debug("Searching both primary sources")
        hits = await _search("video", "primary") + await _search("doc", "primary")
        if hits:
            logger.info(f"Found {len(hits)} relevant chunks in primary sources")
            return _llm_answer(hits, question, kind="mixed")

        # No hits → look in every other video & doc
        logger.info("No relevant information in primary sources, searching all other sources")
        vid_hits = await _search("video", "all")
        doc_hits = await _search("doc",   "all")
        merged   = vid_hits + doc_hits
        if merged:
            logger.info(f"Found {len(merged)} relevant chunks in other sources")

            # collect unique IDs for the fallback notice
            vid_ids = {d.metadata.get("video_id", "unknown") for d in vid_hits}
            doc_ids = {d.metadata.get("doc_id",   "unknown") for d in doc_hits}
            id_parts = []
            if vid_ids:
                id_parts.append(f"videos: {', '.join(sorted(vid_ids))}")
            if doc_ids:
                id_parts.append(f"docs: {', '.join(sorted(doc_ids))}")

            preface = (
                "I couldn't find the answer in the requested video/document, "
                f"but found it in other source(s) ({' | '.join(id_parts)}): "
            )
            result  = _llm_answer(merged, question, kind="mixed")
            result["answer"] = preface + result["answer"]
            return result

    else:  # preferred is "video" **or** "doc"
        logger.debug(f"Searching primary {preferred} source")
        hits = await _search(preferred, "primary")
        if hits:
            logger.info(f"Found relevant information in primary {preferred}")
            return _llm_answer(hits, question, kind=preferred)

        # ── 2️⃣  Look in every *other* item of the preferred kind ───────
        logger.info(f"No relevant information in primary {preferred}, searching other {preferred}s")
        alt_hits = await _search(preferred, "all")
        if alt_hits:
            logger.info(f"Found relevant information in other {preferred}s")

            # collect unique IDs for the fallback notice
            alt_ids = {
                d.metadata.get("video_id" if preferred == "video" else "doc_id", "unknown")
                for d in alt_hits
            }
            preface = (
                f"I couldn't find the answer in the requested {preferred}, "
                f"but found it in other {preferred}(s) ({', '.join(sorted(alt_ids))}): "
            )
            result  = _llm_answer(alt_hits, question, kind=preferred)
            result["answer"] = preface + result["answer"]
            return result

        # ── 3️⃣  Fall back to the opposite store (primary + all) ────────
        opp_kind = "doc" if preferred == "video" else "video"
        opp_hits = (
            await _search(opp_kind, "primary") + await _search(opp_kind, "all")
        )
        if opp_hits:
            # collect unique IDs for the fallback notice
            opp_ids = {
                d.metadata.get("video_id" if opp_kind == "video" else "doc_id", "unknown")
                for d in opp_hits
            }
            preface = (
                f"I couldn’t find the answer in any {preferred}, "
                f"but found one in {opp_kind} ({', '.join(sorted(opp_ids))}): "
            )
            result  = _llm_answer(opp_hits, question, kind=opp_kind)
            result["answer"] = preface + result["answer"]
            return result


    # ── 4️⃣  Nothing relevant anywhere ──────────────────────────────────
    return {
        "answer": "Sorry, I couldn’t find relevant information in any video or document.",
        "citations": [],
    }




# ── helper: build final LLM call & citations ────────────────────────────
def _llm_answer(docs: List[Document], question: str, *, kind: str) -> dict:
    if not docs:
        return {"answer": "Sorry, nothing relevant found.", "citations": []}

    context_lines: list[str] = []
    citations = {"video": [], "doc": []}          # ← plain dict

    for d in docs:
        if (start := d.metadata.get("start")) is not None:   # video chunk
            end = d.metadata["end"]
            ts_range = f"{_sec_to_ts(start)}-{_sec_to_ts(end)}"
            context_lines.append(f"[{ts_range}] {d.page_content}")
            citations["video"].append({"start": start, "end": end})
        else:                                                # doc chunk
            page = d.metadata.get("page_num", 0)
            context_lines.append(f"[Page {page}] {d.page_content}")
            citations["doc"].append({"page": page})

    msg = PROMPT.format(context="\n".join(context_lines), question=question)
    response = chat.invoke(msg)

    return {"answer": response.content.strip(), "citations": citations}

# ── helper: existence check ────────────────────────────────────────────
def _has_points(collection: str, key: str, val: str) -> bool:
    hits = qclient.count(
        collection,
        qmodels.Filter(must=[qmodels.FieldCondition(key=f"metadata.{key}",
                                                    match=qmodels.MatchValue(value=val))])
    )
    return hits.count > 0

def _sec_to_ts(sec: float) -> str:
    m, s = divmod(int(sec), 60)
    return f"{m:02d}:{s:02d}"


async def _filter_relevant_docs(
    docs: List[Document],
    question: str,
) -> List[Document]:
    """
    Run the relevance check for every chunk **in parallel** and keep only the
    ones marked as relevant.  Uses LangChain's `.abatch()` helper to schedule
    the structured-checker calls concurrently.
    """
    # Prepare the inputs for the batch call
    inputs = [
        {"question": question, "chunk": d.page_content}
        for d in docs
    ]

    # Run all checks concurrently
    results: List[RelevanceCheck] = await structured_checker.abatch(inputs)

    # Pick out the relevant documents
    return [
        doc
        for doc, res in zip(docs, results)
        if res["is_relevant"]
    ]

