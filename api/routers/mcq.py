import random
import math
from sqlalchemy.orm import Session
from api.tasks import video_mcq_task, doc_mcq_task
from fastapi import APIRouter, Depends, HTTPException, status
from api.deps import get_api_key
from api.services.mcq import generate_mcqs_for_video, generate_mcqs_for_doc
from api.db import get_db
from api.models import VideoMCQ, DocMCQ

router = APIRouter(prefix="/mcq", tags=["mcq"])


@router.get("/video/{video_id}/", dependencies=[Depends(get_api_key)])
async def get_mcqs(video_id: str, db: Session = Depends(get_db)):
    try:
        row = db.query(VideoMCQ).filter_by(video_id=video_id).first()
        if not row:
            raise HTTPException(status.HTTP_404_NOT_FOUND,
                                detail="MCQs not ready")
        return {
            "video_id": video_id,
            "num_questions": len(row.mcqs),
            "questions": row.mcqs,
        }
    finally:
        db.close()
        
@router.get("/video/{video_id}/random/", dependencies=[Depends(get_api_key)])
async def get_random_subset_mcqs(video_id: str, db: Session = Depends(get_db)):
    """
    Retrieve a random subset of MCQs for the specified video.
    Returns approximately 1/3 of the total questions, rounded up.
    """
    try:
        row = db.query(VideoMCQ).filter_by(video_id=video_id).first()
        if not row:
            raise HTTPException(status.HTTP_404_NOT_FOUND,
                                detail="MCQs not ready")
        
        # Calculate number of questions to return (1/3 of total, rounded up)
        total_mcqs = len(row.mcqs)
        num_to_return = math.ceil(total_mcqs / 3)
        
        # Randomly select questions
        selected_questions = random.sample(row.mcqs, num_to_return)
        
        return {
            "video_id": video_id,
            "num_questions": len(selected_questions),
            "questions": selected_questions,
        }
    finally:
        db.close()
        
@router.post("/video/{video_id}/generate-and-store/", dependencies=[Depends(get_api_key)])
async def generate_and_store_mcqs(video_id: str):
    """
    Trigger background task to generate MCQs for the specified video and store them in the database.
    """
    # Start the Celery task for MCQ generation
    task = video_mcq_task.delay(video_id)
    
    return {
        "video_id": video_id,
        "task_id": task.id,
        "message": "MCQ generation task started. Check task status for completion."
    }

        
@router.get("/video/{video_id}/generate/", dependencies=[Depends(get_api_key)])
async def generate_mcqs_on_demand(video_id: str):
    """
    Generate MCQs for the specified video (fresh each call, no storage).
    """
    try:
        result = await generate_mcqs_for_video(video_id)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result


# ────────────────────────────────────────────────────────────────────────
#  DOCUMENT  MCQ endpoints (mirror video routes)
# ────────────────────────────────────────────────────────────────────────
@router.get("/doc/{doc_id}/", dependencies=[Depends(get_api_key)])
async def get_doc_mcqs(doc_id: str, db: Session = Depends(get_db)):
    try:
        row = db.query(DocMCQ).filter_by(doc_id=doc_id).first()
        if not row:
            raise HTTPException(
                status.HTTP_404_NOT_FOUND, detail="MCQs not ready"
            )
        return {
            "doc_id": doc_id,
            "num_questions": len(row.mcqs),
            "questions": row.mcqs,
        }
    finally:
        db.close()


@router.get("/doc/{doc_id}/random/", dependencies=[Depends(get_api_key)])
async def get_random_subset_doc_mcqs(doc_id: str, db: Session = Depends(get_db)):
    """
    Retrieve a random subset of MCQs for the specified document.
    Returns approximately 1/3 of the total questions, rounded up.
    """
    try:
        row = db.query(DocMCQ).filter_by(doc_id=doc_id).first()
        if not row:
            raise HTTPException(
                status.HTTP_404_NOT_FOUND, detail="MCQs not ready"
            )

        total_mcqs = len(row.mcqs)
        num_to_return = math.ceil(total_mcqs / 3)
        selected = random.sample(row.mcqs, num_to_return)

        return {
            "doc_id": doc_id,
            "num_questions": len(selected),
            "questions": selected,
        }
    finally:
        db.close()


@router.post("/doc/{doc_id}/generate-and-store/", dependencies=[Depends(get_api_key)])
async def generate_and_store_doc_mcqs(doc_id: str):
    """
    Trigger background task to generate MCQs for the specified document
    and store them in the database.
    """
    task = doc_mcq_task.delay(doc_id)
    return {
        "doc_id": doc_id,
        "task_id": task.id,
        "message": "MCQ generation task started. Check task status for completion.",
    }


@router.get("/doc/{doc_id}/generate/", dependencies=[Depends(get_api_key)])
async def generate_doc_mcqs_on_demand(doc_id: str):
    """
    Generate MCQs for the specified document (fresh each call, no storage).
    """
    try:
        result = await generate_mcqs_for_doc(doc_id)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return {
        "doc_id": doc_id,
        "num_questions": len(result["questions"]),
        "questions": result["questions"],
    }