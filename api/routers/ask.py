from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
from pydantic import BaseModel, Field
from api.deps import get_api_key
from api.services.rag import (
    answer_video_question, answer_video_question_with_fallback,
    answer_doc_question, answer_doc_question_with_fallback,
    answer_combined_question, answer_combined_question_with_fallback
)

router = APIRouter(prefix="/ask", tags=["rag"])

# ── payload models ──────────────────────────────────────────────────────
class AskVideoPayload(BaseModel):
    video_id: str = Field(..., description="ID of the video")
    question: str

class AskDocPayload(BaseModel):
    doc_id: str = Field(..., description="ID of the document")
    question: str

class AskCombinedPayload(BaseModel):
    video_id: str
    doc_id: str
    question: str

# ── endpoints ───────────────────────────────────────────────────────────
@router.post("/video/", dependencies=[Depends(get_api_key)])
async def ask_video(payload: AskVideoPayload):
    try:
        result = await answer_video_question(payload.video_id, payload.question)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result

@router.post("/video-with-fallback/", dependencies=[Depends(get_api_key)])
async def ask_video(payload: AskVideoPayload):
    try:
        result = await answer_video_question_with_fallback(payload.video_id, payload.question)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result

@router.post("/doc/", dependencies=[Depends(get_api_key)])
async def ask_doc(payload: AskDocPayload):
    try:
        result = await answer_doc_question(payload.doc_id, payload.question)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result

@router.post("/doc-with-fallback/", dependencies=[Depends(get_api_key)])
async def ask_doc(payload: AskDocPayload):
    try:
        result = await answer_doc_question_with_fallback(payload.doc_id, payload.question)
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result

@router.post("/combined/", dependencies=[Depends(get_api_key)])
async def ask_combined(payload: AskCombinedPayload):
    try:
        result = await answer_combined_question(
            payload.video_id, payload.doc_id, payload.question
        )
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result

@router.post("/combined-with-fallback/", dependencies=[Depends(get_api_key)])
async def ask_combined(payload: AskCombinedPayload):
    try:
        result = await answer_combined_question_with_fallback(
            payload.video_id, payload.doc_id, payload.question
        )
    except ValueError as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    return result
