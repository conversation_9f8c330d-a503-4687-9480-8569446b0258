## `routers/` Directory

Contains FastAPI route definitions:

```
├── routers/
│   ├── ask.py
│   ├── health.py
│   ├── task_status.py
│   ├── upload.py
|   ├── video.py
│   └── __init__.py
```

### `health.py`

Defines a GET endpoint at `/health` using FastAPI’s `APIRouter`. It returns a JSON response:

```json
{ "status": "ok" }
```

Indicating that the API service is running correctly.

### `ask.py`

Defines three POST endpoints under `/ask/` for handling question-answering using Retrieval-Augmented Generation (RAG). These endpoints allow questions to be asked based on:

- Video content
- Document content
- A combination of both

All routes require an API key, enforced via `Depends(get_api_key)`.

#### Video Question Endpoint – `POST /ask/video`

- Accepts a JSON payload with:

  - `video_id`: The ID of the video
  - `question`: The question to be answered based on the video content

- Calls `answer_video_question()` from `services/rag.py` to generate the response
- Returns the answer in JSON format
- Raises a `404 Not Found` error if the video is not found

#### Document Question Endpoint – `POST /ask/doc`

- Accepts a JSON payload with:

  - `doc_id`: The ID of the document
  - `question`: The question to be answered based on the document content

- Calls `answer_doc_question()` from `services/rag.py`
- Returns the generated answer
- Raises a `404 Not Found` error if the document is not found

#### Combined Question Endpoint – `POST /ask/combined`

- Accepts a JSON payload with:

  - `video_id`: The ID of the video
  - `doc_id`: The ID of the document
  - `question`: The question to be answered using both video and document context

- Calls `answer_combined_question()` from `services/rag.py`
- Returns the generated answer
- Raises a `404 Not Found` error if either source is not found

### `task_status.py`

Defines a GET endpoint at `/task/{task_id}` for checking the status of background tasks:

- Uses `Depends(get_api_key)` to ensure the request includes a valid API key.
- Accepts a `task_id` as a path parameter.
- Uses `AsyncResult` from Celery to fetch the task's current state and result.
- Returns the task status (`PENDING`, `SUCCESS`, `FAILURE`, etc.) along with the result or error message if applicable.

### `upload.py`

Handles uploads and queues them for background processing via Celery:

#### Video Upload Endpoint – `POST /upload/`

- Secured using an API key with `Depends(get_api_key)`.
- Accepts:

  - A video file (`file`)
  - A `video_id` (as a `Form` field)

- Saves the uploaded file to a shared volume at `/data/uploads`, using the naming format `{video_id}_{filename}`.
- Queues a transcription task using `transcribe_task.delay()` with the saved file path and `video_id`.
- Returns a JSON response with:
  - `video_id`
  - Celery `task_id`
  - Confirmation message

#### Document Upload Endpoint – `POST /upload/doc`

- Secured with `Depends(get_api_key)`.
- Accepts:

  - A document file (`file`) – supported formats: `.pdf`, `.ppt`, `.pptx`
  - A `doc_id` (as a `Form` field)

- Validates the file extension and raises an HTTP 400 error if unsupported.
- Saves the file to `/data/uploads` using the format `{doc_id}_{filename}`.
- Queues a document ingestion task using `doc_ingestion_task.delay()` with the file path and `doc_id`.
- Returns a JSON response with:
  - `doc_id`
  - Celery `task_id`
  - Confirmation message

### `videos.py`

Defines a `GET` endpoint at `/videos/{video_id}` to stream uploaded videos with support for HTTP Range requests, enabling browser-based video seeking.

- Uses FastAPI’s `APIRouter` with prefix `/videos`.
- Looks for uploaded video files in the `/data/uploads` directory.
- Video files are expected to be named using the format `{video_id}_<original_filename>`.

#### Video Stream Endpoint – `GET /videos/{video_id}`

- Accepts:

  - `video_id`: The unique identifier of the uploaded video (path parameter)
  - `range` header (optional): For partial content delivery (byte-range streaming)

- Behavior:

  - If no `Range` header is provided, returns the full video as a `FileResponse` with `video/mp4` content type.
  - If a valid `Range` header is present, returns a `206 Partial Content` response using `StreamingResponse` with appropriate headers (`Content-Range`, `Content-Length`, `Accept-Ranges`).
  - Efficiently streams the file in 1 MB chunks to reduce memory usage.

- Raises:

  - `404 Not Found` if the video file matching `video_id` is not found in the uploads directory
  - `416 Requested Range Not Satisfiable` if the requested byte range is invalid or exceeds file size

### `__init__.py`

Initializes the Python package.
