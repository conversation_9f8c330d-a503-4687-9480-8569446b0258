import uuid, shutil
from pathlib import Path
from fastapi import APIRouter, UploadFile, Depends, Form, HTTPException, status
from ..deps import get_api_key
from celery import chain
from api.tasks import transcribe_task, video_mcq_task, doc_ingestion_task, doc_mcq_task

router = APIRouter(prefix="/upload", tags=["upload"])
UPLOAD_DIR = Path("/data/uploads")   # shared volume path
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

@router.post("/video/", dependencies=[Depends(get_api_key)])
async def upload_video(file: UploadFile, video_id: str = Form(...)):
    dest = UPLOAD_DIR / f"{video_id}_{file.filename}"
    with dest.open("wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    # build Celery chain: transcribe then mcq
    job = chain(
        transcribe_task.s(str(dest), video_id),   # returns None
        video_mcq_task.si(video_id)                      # uses video_id, ignores previous result
    ).apply_async()

    transcribe_id = job.parent.id    # first task in the chain
    mcq_id        = job.id           # last task (mcq_task)

    return {
        "video_id": video_id,
        "transcribe_task_id": transcribe_id,
        "mcq_task_id": mcq_id,
        "message": "Uploaded - transcription and MCQ generation queued",
    }
    
@router.post("/doc/", dependencies=[Depends(get_api_key)])
async def upload_text(file: UploadFile, doc_id: str = Form(...)):
    allowed_types = [".pdf", ".ppt", ".pptx"]
    ext = Path(file.filename).suffix.lower()
    
    if ext not in allowed_types:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, 
                            detail=f"Unsupported file type: {ext}. Allowed: {allowed_types}")
    
    dest = UPLOAD_DIR / f"{doc_id}_{file.filename}"
    with dest.open("wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
        
    job = chain(
        doc_ingestion_task.s(str(dest), doc_id),
        doc_mcq_task.si(doc_id)              # use .si to ignore prev result
    ).apply_async()
        
    return {
        "doc_id": doc_id,
        "ingest_task_id": job.parent.id,
        "mcq_task_id": job.id,
        "message": f"Uploaded – ingestion & MCQ generation queued"
    }