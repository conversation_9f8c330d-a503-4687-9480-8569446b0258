import uuid, shutil
from pathlib import Path
from fastapi import APIRouter, UploadFile, Depends, Form, HTTPException, status
from ..deps import get_api_key
from celery import chain
from api.tasks_cloud import transcribe_task_cloud, video_mcq_task_cloud, doc_ingestion_task_cloud, doc_mcq_task_cloud
from api.services.cloud_storage import get_cloud_storage, generate_cloud_key
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/upload-cloud", tags=["upload-cloud"])

@router.post("/video/", dependencies=[Depends(get_api_key)])
async def upload_video_cloud(file: UploadFile, video_id: str = Form(...)):
    """Upload video to cloud storage and queue transcription and MCQ generation."""
    try:
        # Get cloud storage instance
        cloud_storage = get_cloud_storage()
        
        # Generate cloud storage key
        cloud_key = generate_cloud_key(video_id, file.filename, "videos")
        
        # Upload file to cloud storage
        file.file.seek(0)  # Reset file pointer
        cloud_url = cloud_storage.upload_file(file.file, cloud_key)
        
        logger.info(f"Successfully uploaded video {video_id} to cloud storage: {cloud_url}")
        
        # build Celery chain: transcribe then mcq
        job = chain(
            transcribe_task_cloud.s(cloud_key, video_id),   # returns None
            video_mcq_task_cloud.si(video_id)               # uses video_id, ignores previous result
        ).apply_async()

        transcribe_id = job.parent.id    # first task in the chain
        mcq_id        = job.id           # last task (mcq_task)

        return {
            "video_id": video_id,
            "cloud_url": cloud_url,
            "cloud_key": cloud_key,
            "transcribe_task_id": transcribe_id,
            "mcq_task_id": mcq_id,
            "message": "Uploaded to cloud storage - transcription and MCQ generation queued",
        }
        
    except Exception as e:
        logger.error(f"Failed to upload video {video_id} to cloud storage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload video to cloud storage: {str(e)}"
        )
    
@router.post("/doc/", dependencies=[Depends(get_api_key)])
async def upload_doc_cloud(file: UploadFile, doc_id: str = Form(...)):
    """Upload document to cloud storage and queue ingestion and MCQ generation."""
    allowed_types = [".pdf", ".ppt", ".pptx"]
    ext = Path(file.filename).suffix.lower()
    
    if ext not in allowed_types:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, 
                            detail=f"Unsupported file type: {ext}. Allowed: {allowed_types}")
    
    try:
        # Get cloud storage instance
        cloud_storage = get_cloud_storage()
        
        # Generate cloud storage key
        cloud_key = generate_cloud_key(doc_id, file.filename, "documents")
        
        # Upload file to cloud storage
        file.file.seek(0)  # Reset file pointer
        cloud_url = cloud_storage.upload_file(file.file, cloud_key)
        
        logger.info(f"Successfully uploaded document {doc_id} to cloud storage: {cloud_url}")
        
        job = chain(
            doc_ingestion_task_cloud.s(cloud_key, doc_id),
            doc_mcq_task_cloud.si(doc_id)              # use .si to ignore prev result
        ).apply_async()
            
        return {
            "doc_id": doc_id,
            "cloud_url": cloud_url,
            "cloud_key": cloud_key,
            "ingest_task_id": job.parent.id,
            "mcq_task_id": job.id,
            "message": f"Uploaded to cloud storage – ingestion & MCQ generation queued"
        }
        
    except Exception as e:
        logger.error(f"Failed to upload document {doc_id} to cloud storage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload document to cloud storage: {str(e)}"
        )
