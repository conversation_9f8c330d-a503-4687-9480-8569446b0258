from fastapi import APIRouter, Depends, HTTPException
from celery.result import AsyncResult
from ..deps import get_api_key
from ..celery_app import celery_app

router = APIRouter(prefix="/task", tags=["tasks"])

@router.get("/{task_id}/", dependencies=[Depends(get_api_key)])
def task_status(task_id: str):
    res = AsyncResult(task_id, app=celery_app)
    if res.state == "PENDING":
        return {"state": res.state}
    if res.state == "FAILURE":
        return {"state": res.state, "error": str(res.result)}
    # SUCCESS or other states
    return {"state": res.state, "result": res.result}
