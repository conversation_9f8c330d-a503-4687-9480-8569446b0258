from fastapi import APIRouter, Request, HTTPException, Response, status
from fastapi.responses import StreamingResponse, FileResponse
from pathlib import Path
import re, os, tempfile
from api.services.cloud_storage import get_cloud_storage, generate_cloud_key
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/videos-cloud", tags=["videos-cloud"])
RANGE_RE = re.compile(r"bytes=(\d+)-(\d+)?")   # e.g. "bytes=1000000-"

CHUNK_SIZE = 1024 * 1024  # 1 MB per iteration


@router.get("/{video_id}/")
def video_stream_cloud(video_id: str, request: Request):
    """Serve video from cloud storage with HTTP Range support so the browser can seek."""
    try:
        # Get cloud storage instance
        cloud_storage = get_cloud_storage()
        
        # Try to find the video file in cloud storage
        # We need to search for files with the video_id prefix
        # For now, we'll assume the filename pattern and try common video extensions
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        cloud_key = None
        
        # Try to find the video file by checking common patterns
        for ext in video_extensions:
            test_key = f"videos/{video_id}_{video_id}{ext}"  # Common pattern
            if cloud_storage.file_exists(test_key):
                cloud_key = test_key
                break
        
        # If not found with common pattern, try a more generic search
        if cloud_key is None:
            # For a more robust implementation, you might want to store the cloud_key 
            # in a database when uploading, but for now we'll try a few patterns
            test_patterns = [
                f"videos/{video_id}_video{ext}" for ext in video_extensions
            ] + [
                f"videos/{video_id}_upload{ext}" for ext in video_extensions
            ]
            
            for pattern in test_patterns:
                if cloud_storage.file_exists(pattern):
                    cloud_key = pattern
                    break
        
        if cloud_key is None:
            raise HTTPException(404, "Video not found in cloud storage")
        
        # Get file size from cloud storage
        file_size = cloud_storage.get_file_size(cloud_key)
        range_header = request.headers.get("range")

        # ── 1. No Range  →  full file (simple GET) ──────────────────────────
        if range_header is None:
            # Download the entire file and serve it
            temp_path = cloud_storage.download_file(cloud_key)
            
            def cleanup_temp_file():
                try:
                    os.unlink(temp_path)
                except:
                    pass
            
            # Note: In a production environment, you might want to implement
            # a more efficient streaming solution that doesn't download the entire file
            response = FileResponse(temp_path, media_type="video/mp4")
            response.background = cleanup_temp_file
            return response

        # ── 2. Parse Range header ───────────────────────────────────────────
        match = RANGE_RE.match(range_header)
        if not match:
            raise HTTPException(416, "Invalid Range header")

        start = int(match.group(1))
        end = int(match.group(2) or file_size - 1)

        if start >= file_size or end >= file_size:
            raise HTTPException(416, "Requested range not satisfiable")

        length = end - start + 1

        # ── 3. Cloud file iterator ──────────────────────────────────────────
        def iter_cloud_file(cloud_key: str, start_pos: int, length: int):
            """
            Iterator for streaming file chunks from cloud storage.
            Note: This implementation downloads the file first, which is not optimal.
            For production, consider using cloud provider's range request capabilities.
            """
            # Download file to temporary location
            temp_path = cloud_storage.download_file(cloud_key)
            
            try:
                with open(temp_path, "rb") as f:
                    f.seek(start_pos)
                    remaining = length
                    while remaining > 0:
                        chunk = f.read(min(CHUNK_SIZE, remaining))
                        if not chunk:
                            break
                        remaining -= len(chunk)
                        yield chunk
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_path)
                except:
                    pass

        headers = {
            "Accept-Ranges": "bytes",
            "Content-Range": f"bytes {start}-{end}/{file_size}",
            "Content-Length": str(length),
            "Content-Type": "video/mp4",
        }

        return StreamingResponse(
            iter_cloud_file(cloud_key, start, length),
            status_code=status.HTTP_206_PARTIAL_CONTENT,
            headers=headers,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error streaming video {video_id} from cloud storage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stream video from cloud storage: {str(e)}"
        )


@router.get("/{video_id}/info/")
def video_info_cloud(video_id: str):
    """Get video information from cloud storage."""
    try:
        cloud_storage = get_cloud_storage()
        
        # Try to find the video file (same logic as in video_stream_cloud)
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        cloud_key = None
        
        for ext in video_extensions:
            test_key = f"videos/{video_id}_{video_id}{ext}"
            if cloud_storage.file_exists(test_key):
                cloud_key = test_key
                break
        
        if cloud_key is None:
            test_patterns = [
                f"videos/{video_id}_video{ext}" for ext in video_extensions
            ] + [
                f"videos/{video_id}_upload{ext}" for ext in video_extensions
            ]
            
            for pattern in test_patterns:
                if cloud_storage.file_exists(pattern):
                    cloud_key = pattern
                    break
        
        if cloud_key is None:
            raise HTTPException(404, "Video not found in cloud storage")
        
        file_size = cloud_storage.get_file_size(cloud_key)
        file_url = cloud_storage.get_file_url(cloud_key)
        
        return {
            "video_id": video_id,
            "cloud_key": cloud_key,
            "file_size": file_size,
            "file_url": file_url,
            "exists": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting video info for {video_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get video info: {str(e)}"
        )
