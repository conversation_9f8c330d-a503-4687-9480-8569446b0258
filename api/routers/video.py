from fastapi import APIRouter, Request, HTTPException, Response, status
from fastapi.responses import StreamingResponse, FileResponse
from pathlib import Path
import re, os

router = APIRouter(prefix="/videos", tags=["videos"])
UPLOAD_DIR = Path("/data/uploads")
RANGE_RE = re.compile(r"bytes=(\d+)-(\d+)?")   # e.g. "bytes=1000000-"

CHUNK_SIZE = 1024 * 1024  # 1 MB per iteration


@router.get("/{video_id}/")
def video_stream(video_id: str, request: Request):
    """Serve video with HTTP Range support so the browser can seek."""
    try:
        path = next(UPLOAD_DIR.glob(f"{video_id}_*"))
    except StopIteration:
        raise HTTPException(404, "Video not found")

    file_size = path.stat().st_size
    range_header = request.headers.get("range")

    # ── 1. No Range  →  full file (simple GET) ──────────────────────────
    if range_header is None:
        return FileResponse(path, media_type="video/mp4")

    # ── 2. Parse Range header ───────────────────────────────────────────
    match = RANGE_RE.match(range_header)
    if not match:
        raise HTTPException(416, "Invalid Range header")

    start = int(match.group(1))
    end = int(match.group(2) or file_size - 1)

    if start >= file_size or end >= file_size:
        raise HTTPException(416, "Requested range not satisfiable")

    length = end - start + 1

    # ── 3. File iterator ────────────────────────────────────────────────
    def iter_file(fn: Path, start_pos: int, length: int):
        with fn.open("rb") as f:
            f.seek(start_pos)
            remaining = length
            while remaining > 0:
                chunk = f.read(min(CHUNK_SIZE, remaining))
                if not chunk:
                    break
                remaining -= len(chunk)
                yield chunk

    headers = {
        "Accept-Ranges": "bytes",
        "Content-Range": f"bytes {start}-{end}/{file_size}",
        "Content-Length": str(length),
        "Content-Type": "video/mp4",
    }

    return StreamingResponse(
        iter_file(path, start, length),
        status_code=status.HTTP_206_PARTIAL_CONTENT,
        headers=headers,
    )
