## Mnemos-AI Documentation

This is the documentation for the **Mnemos-AI** backend.

## Prerequisites

Ensure the following are installed on your system:

- [Docker](https://www.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)

## Set-Up

1. Clone the repository and navigate into it.
2. Create a `.env` file by copying and editing the provided `example.env` template.

   > Place your `.env` file in the **root directory** of the project.

3. Build the Docker image by running:

```bash
docker compose build
```

4. Once the image is built, you can start the application with:

```bash
docker compose up
```

## Repository Structure

```
mnemos-ai
├── api/
│   ├── README.md
│   ├── celery_app.py
│   ├── deps.py
│   ├── main.py
│   ├── provider_factory.py
│   ├── tasks.py
│   ├── __init__.py
│   ├── routers/
│   │   ├── ask.py
│   │   ├── health.py
│   │   ├── task_status.py
│   |   ├── video.py
|   │   ├── upload.py
│   │   └── __init__.py
│   └── services/
│       ├── rag.py
│       ├── transcribe.py
|       |── doc_ingest.py
│       └── __init__.py
├── .env
├── .gitignore
├── Dockerfile
├── docker-compose.yaml
├── example.env
├── poetry.lock
├── pyproject.toml
├── README.md
└── __init__.py
```

- [api folder](api/): Contains the backend application code. Refer to the [api folder](api/) for detailed API documentation.
- `docker-compose.yml`: Defines the services and builds the Docker image.
- `Dockerfile`: Installs dependencies using Poetry, copies files from the `api` folder, and runs the FastAPI app with Uvicorn.
- `pyproject.toml` & `poetry.lock`: Used to manage and lock Python dependencies.
- `example.env`: Template for environment variables used in the application.
- `__init__.py`: Initializes the Python package.

## Final Notes

This documentation provides everything needed to set up and run the Mnemos-AI backend.
